use tracing::info;
use tracing_subscriber;

#[tokio::main]
async fn main() -> std::result::Result<(), Box<dyn std::error::Error>> {
    // Initialize logging
    tracing_subscriber::fmt()
        .with_env_filter("tun_proxy=debug,info")
        .init();

    info!("Starting TUN Proxy application");

    // This is a placeholder for the standalone CLI version
    // The main application logic will be in the Tauri backend
    println!("TUN Proxy - Use the GUI application for full functionality");

    Ok(())
}