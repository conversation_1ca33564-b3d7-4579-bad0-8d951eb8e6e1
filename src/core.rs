use std::net::SocketAddr;
use tokio::io::{AsyncRead, AsyncWrite};
use async_trait::async_trait;
use crate::error::ProxyError;

/// Represents a connection request with source and destination addresses
#[derive(Debug, <PERSON><PERSON>)]
pub struct ConnectionRequest {
    pub source: SocketAddr,
    pub destination: SocketAddr,
}

/// Trait for proxied TCP streams that can be read from and written to
pub trait ProxyTcpStream: AsyncRead + AsyncWrite + Send + Unpin {}

/// Trait for handling UDP associations through a proxy
#[async_trait]
pub trait ProxyUdpHandler: Send + Sync {
    /// Send data to a target address through the proxy
    async fn send_to(&self, data: &[u8], target: SocketAddr) -> std::io::Result<()>;
    
    /// Receive data from the proxy, returning the data size and source address
    async fn recv_from(&self, buf: &mut [u8]) -> std::io::Result<(usize, SocketAddr)>;
}

/// Core proxy trait that defines the interface between the network layer and proxy implementation
#[async_trait]
pub trait CoreProxy: Send + Sync {
    /// Initialize the proxy with configuration data
    fn configure(&mut self, config_data: &str) -> Result<(), ProxyError>;

    /// Get available node names for UI selection
    fn get_node_names(&self) -> Vec<String>;

    /// Get all server IP addresses for routing exemptions
    fn get_all_server_ips(&self) -> Vec<std::net::IpAddr>;

    /// Handle a TCP connection request through a specific node
    async fn handle_tcp_stream(
        &self,
        request: ConnectionRequest,
        node_name: &str,
    ) -> Result<Box<dyn ProxyTcpStream>, ProxyError>;

    /// Handle a UDP association request through a specific node
    async fn handle_udp_association(
        &self,
        request: ConnectionRequest,
        node_name: &str,
    ) -> Result<Box<dyn ProxyUdpHandler>, ProxyError>;
}