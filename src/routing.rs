use std::net::IpAddr;
use std::process::Command;
use crate::error::{Result, TunProxyError};
use tracing::{debug, info, warn};

/// Cross-platform routing table manager
pub struct RoutingManager {
    original_routes: Vec<RouteEntry>,
    tun_routes_added: Vec<RouteEntry>,
}

#[derive(Debug, Clone)]
pub struct RouteEntry {
    pub destination: String,
    pub gateway: String,
    pub interface: String,
    pub metric: Option<u32>,
}

impl RoutingManager {
    pub fn new() -> Self {
        Self {
            original_routes: Vec::new(),
            tun_routes_added: Vec::new(),
        }
    }

    /// Backup current routing table
    pub async fn backup_routes(&mut self) -> Result<()> {
        info!("Backing up current routing table");
        self.original_routes = self.get_current_routes().await?;
        debug!("Backed up {} routes", self.original_routes.len());
        Ok(())
    }

    /// Restore original routing table
    pub async fn restore_routes(&mut self) -> Result<()> {
        info!("Restoring original routing table");

        // Remove TUN routes we added
        for route in &self.tun_routes_added {
            if let Err(e) = self.remove_route(route).await {
                warn!("Failed to remove TUN route: {}", e);
            }
        }

        self.tun_routes_added.clear();
        info!("Routing table restored");
        Ok(())
    }

    /// Add route for proxy server to bypass TUN
    pub async fn add_proxy_server_route(&mut self, server_ip: IpAddr) -> Result<()> {
        debug!("Adding route for proxy server: {}", server_ip);

        let gateway = self.get_default_gateway().await?;
        let interface = self.get_default_interface().await?;

        let route = RouteEntry {
            destination: server_ip.to_string(),
            gateway,
            interface,
            metric: Some(1),
        };

        self.add_route(&route).await?;
        self.tun_routes_added.push(route);

        Ok(())
    }

    /// Set default route to TUN interface
    pub async fn set_default_route_to_tun(&mut self, tun_interface: &str) -> Result<()> {
        info!("Setting default route to TUN interface: {}", tun_interface);

        // Add split routes (0.0.0.0/1 and *********/1) to avoid conflicts
        let routes = vec![
            RouteEntry {
                destination: "0.0.0.0/1".to_string(),
                gateway: "********".to_string(),
                interface: tun_interface.to_string(),
                metric: Some(1),
            },
            RouteEntry {
                destination: "*********/1".to_string(),
                gateway: "********".to_string(),
                interface: tun_interface.to_string(),
                metric: Some(1),
            },
        ];

        for route in routes {
            self.add_route(&route).await?;
            self.tun_routes_added.push(route);
        }

        Ok(())
    }

    /// Get current routing table
    async fn get_current_routes(&self) -> Result<Vec<RouteEntry>> {
        #[cfg(target_os = "windows")]
        {
            self.get_windows_routes().await
        }

        #[cfg(target_os = "macos")]
        {
            self.get_macos_routes().await
        }

        #[cfg(target_os = "linux")]
        {
            self.get_linux_routes().await
        }

        #[cfg(not(any(target_os = "windows", target_os = "macos", target_os = "linux")))]
        {
            Err(TunProxyError::RoutingError("Unsupported platform".to_string()))
        }
    }

    /// Get default gateway
    async fn get_default_gateway(&self) -> Result<String> {
        #[cfg(target_os = "windows")]
        {
            let output = Command::new("cmd")
                .args(&["/C", "route print 0.0.0.0"])
                .output()
                .map_err(|e| TunProxyError::RoutingError(format!("Failed to get default gateway: {}", e)))?;

            let output_str = String::from_utf8_lossy(&output.stdout);
            // Parse Windows route output to extract default gateway
            for line in output_str.lines() {
                if line.contains("0.0.0.0") && line.contains("0.0.0.0") {
                    let parts: Vec<&str> = line.split_whitespace().collect();
                    if parts.len() >= 3 {
                        return Ok(parts[2].to_string());
                    }
                }
            }
            Err(TunProxyError::RoutingError("Could not find default gateway".to_string()))
        }

        #[cfg(target_os = "macos")]
        {
            let output = Command::new("route")
                .args(&["-n", "get", "default"])
                .output()
                .map_err(|e| TunProxyError::RoutingError(format!("Failed to get default gateway: {}", e)))?;

            let output_str = String::from_utf8_lossy(&output.stdout);
            for line in output_str.lines() {
                if line.trim().starts_with("gateway:") {
                    let gateway = line.split(':').nth(1)
                        .ok_or_else(|| TunProxyError::RoutingError("Invalid gateway format".to_string()))?
                        .trim();
                    return Ok(gateway.to_string());
                }
            }
            Err(TunProxyError::RoutingError("Could not find default gateway".to_string()))
        }

        #[cfg(target_os = "linux")]
        {
            let output = Command::new("ip")
                .args(&["route", "show", "default"])
                .output()
                .map_err(|e| TunProxyError::RoutingError(format!("Failed to get default gateway: {}", e)))?;

            let output_str = String::from_utf8_lossy(&output.stdout);
            for line in output_str.lines() {
                if line.contains("default via") {
                    let parts: Vec<&str> = line.split_whitespace().collect();
                    if parts.len() >= 3 {
                        return Ok(parts[2].to_string());
                    }
                }
            }
            Err(TunProxyError::RoutingError("Could not find default gateway".to_string()))
        }

        #[cfg(not(any(target_os = "windows", target_os = "macos", target_os = "linux")))]
        {
            Err(TunProxyError::RoutingError("Unsupported platform".to_string()))
        }
    }

    /// Get default network interface
    async fn get_default_interface(&self) -> Result<String> {
        #[cfg(target_os = "windows")]
        {
            // For Windows, we'll use a generic interface name
            Ok("Ethernet".to_string())
        }

        #[cfg(target_os = "macos")]
        {
            let output = Command::new("route")
                .args(&["-n", "get", "default"])
                .output()
                .map_err(|e| TunProxyError::RoutingError(format!("Failed to get default interface: {}", e)))?;

            let output_str = String::from_utf8_lossy(&output.stdout);
            for line in output_str.lines() {
                if line.trim().starts_with("interface:") {
                    let interface = line.split(':').nth(1)
                        .ok_or_else(|| TunProxyError::RoutingError("Invalid interface format".to_string()))?
                        .trim();
                    return Ok(interface.to_string());
                }
            }
            Err(TunProxyError::RoutingError("Could not find default interface".to_string()))
        }

        #[cfg(target_os = "linux")]
        {
            let output = Command::new("ip")
                .args(&["route", "show", "default"])
                .output()
                .map_err(|e| TunProxyError::RoutingError(format!("Failed to get default interface: {}", e)))?;

            let output_str = String::from_utf8_lossy(&output.stdout);
            for line in output_str.lines() {
                if line.contains("dev") {
                    let parts: Vec<&str> = line.split_whitespace().collect();
                    if let Some(dev_index) = parts.iter().position(|&x| x == "dev") {
                        if dev_index + 1 < parts.len() {
                            return Ok(parts[dev_index + 1].to_string());
                        }
                    }
                }
            }
            Err(TunProxyError::RoutingError("Could not find default interface".to_string()))
        }

        #[cfg(not(any(target_os = "windows", target_os = "macos", target_os = "linux")))]
        {
            Err(TunProxyError::RoutingError("Unsupported platform".to_string()))
        }
    }

    /// Add a route to the routing table
    async fn add_route(&self, route: &RouteEntry) -> Result<()> {
        debug!("Adding route: {} via {} dev {}", route.destination, route.gateway, route.interface);

        #[cfg(target_os = "windows")]
        {
            let cmd = format!(
                "route add {} mask 255.255.255.255 {} metric {}",
                route.destination,
                route.gateway,
                route.metric.unwrap_or(1)
            );
            self.execute_command(&cmd).await
        }

        #[cfg(target_os = "macos")]
        {
            let cmd = format!(
                "route add -net {} -gateway {} -interface {}",
                route.destination, route.gateway, route.interface
            );
            self.execute_command(&cmd).await
        }

        #[cfg(target_os = "linux")]
        {
            let cmd = format!(
                "ip route add {} via {} dev {}",
                route.destination, route.gateway, route.interface
            );
            self.execute_command(&cmd).await
        }

        #[cfg(not(any(target_os = "windows", target_os = "macos", target_os = "linux")))]
        {
            Err(TunProxyError::RoutingError("Unsupported platform".to_string()))
        }
    }

    /// Remove a route from the routing table
    async fn remove_route(&self, route: &RouteEntry) -> Result<()> {
        debug!("Removing route: {}", route.destination);

        #[cfg(target_os = "windows")]
        {
            let cmd = format!("route delete {}", route.destination);
            self.execute_command(&cmd).await
        }

        #[cfg(target_os = "macos")]
        {
            let cmd = format!("route delete -net {}", route.destination);
            self.execute_command(&cmd).await
        }

        #[cfg(target_os = "linux")]
        {
            let cmd = format!("ip route del {}", route.destination);
            self.execute_command(&cmd).await
        }

        #[cfg(not(any(target_os = "windows", target_os = "macos", target_os = "linux")))]
        {
            Err(TunProxyError::RoutingError("Unsupported platform".to_string()))
        }
    }

    /// Execute a system command
    async fn execute_command(&self, cmd: &str) -> Result<()> {
        debug!("Executing routing command: {}", cmd);

        #[cfg(target_os = "windows")]
        let output = Command::new("cmd").args(&["/C", cmd]).output();

        #[cfg(not(target_os = "windows"))]
        let output = Command::new("sh").args(&["-c", cmd]).output();

        let output = output
            .map_err(|e| TunProxyError::RoutingError(format!("Failed to execute command: {}", e)))?;

        if !output.status.success() {
            let stderr = String::from_utf8_lossy(&output.stderr);
            return Err(TunProxyError::RoutingError(format!("Command failed: {}", stderr)));
        }

        Ok(())
    }

    // Platform-specific route parsing methods
    #[cfg(target_os = "windows")]
    async fn get_windows_routes(&self) -> Result<Vec<RouteEntry>> {
        // Implementation for parsing Windows routing table
        Ok(Vec::new()) // Placeholder
    }

    #[cfg(target_os = "macos")]
    async fn get_macos_routes(&self) -> Result<Vec<RouteEntry>> {
        // Implementation for parsing macOS routing table
        Ok(Vec::new()) // Placeholder
    }

    #[cfg(target_os = "linux")]
    async fn get_linux_routes(&self) -> Result<Vec<RouteEntry>> {
        // Implementation for parsing Linux routing table
        Ok(Vec::new()) // Placeholder
    }
}

impl Default for RoutingManager {
    fn default() -> Self {
        Self::new()
    }
}