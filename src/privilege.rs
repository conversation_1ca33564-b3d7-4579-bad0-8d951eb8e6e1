use crate::error::{Result, TunProxyError};
use std::process::Command;
use tracing::{debug, info, error};

/// Privilege management for system operations
pub struct PrivilegeManager;

impl PrivilegeManager {
    /// Check if the application has the necessary privileges
    pub fn check_privileges() -> Result<()> {
        #[cfg(target_os = "macos")]
        {
            Self::check_macos_privileges()
        }

        #[cfg(target_os = "linux")]
        {
            Self::check_linux_privileges()
        }

        #[cfg(target_os = "windows")]
        {
            Self::check_windows_privileges()
        }

        #[cfg(not(any(target_os = "macos", target_os = "linux", target_os = "windows")))]
        {
            Err(TunProxyError::RoutingError("Unsupported platform".to_string()))
        }
    }

    /// Execute a command with elevated privileges if needed
    pub async fn execute_privileged_command(cmd: &str) -> Result<()> {
        debug!("Executing privileged command: {}", cmd);

        #[cfg(target_os = "macos")]
        {
            Self::execute_macos_command(cmd).await
        }

        #[cfg(target_os = "linux")]
        {
            Self::execute_linux_command(cmd).await
        }

        #[cfg(target_os = "windows")]
        {
            Self::execute_windows_command(cmd).await
        }

        #[cfg(not(any(target_os = "macos", target_os = "linux", target_os = "windows")))]
        {
            Err(TunProxyError::RoutingError("Unsupported platform".to_string()))
        }
    }

    /// Get instructions for running with proper privileges
    pub fn get_privilege_instructions() -> String {
        #[cfg(target_os = "macos")]
        {
            "On macOS, TUN Proxy requires administrator privileges to create TUN devices and modify routing tables.\n\n\
            Please run the application with sudo or grant it the necessary permissions:\n\
            1. Run: sudo ./tun-proxy\n\
            2. Or grant the app permission to modify network settings in System Preferences\n\
            3. Alternatively, you can run: sudo npm run tauri dev".to_string()
        }

        #[cfg(target_os = "linux")]
        {
            "On Linux, TUN Proxy requires root privileges or CAP_NET_ADMIN capability.\n\n\
            Please run with one of the following options:\n\
            1. Run: sudo ./tun-proxy\n\
            2. Or grant CAP_NET_ADMIN: sudo setcap cap_net_admin+ep ./tun-proxy\n\
            3. For development: sudo npm run tauri dev".to_string()
        }

        #[cfg(target_os = "windows")]
        {
            "On Windows, TUN Proxy requires administrator privileges.\n\n\
            Please run the application as Administrator:\n\
            1. Right-click the application and select 'Run as administrator'\n\
            2. Or run from an elevated command prompt".to_string()
        }

        #[cfg(not(any(target_os = "macos", target_os = "linux", target_os = "windows")))]
        {
            "This platform is not supported.".to_string()
        }
    }

    #[cfg(target_os = "macos")]
    fn check_macos_privileges() -> Result<()> {
        debug!("Checking macOS privileges...");
        
        // Check if running as root
        let output = Command::new("id")
            .arg("-u")
            .output()
            .map_err(|e| TunProxyError::RoutingError(format!("Failed to check user ID: {}", e)))?;

        let uid_string = String::from_utf8_lossy(&output.stdout);
        let uid = uid_string.trim();
        
        debug!("Current UID: {}", uid);
        
        if uid == "0" {
            info!("Running with root privileges");
            return Ok(());
        }

        // Check if we can use sudo without password
        let sudo_check = Command::new("sudo")
            .args(&["-n", "true"])
            .output()
            .map_err(|e| TunProxyError::RoutingError(format!("Failed to check sudo access: {}", e)))?;

        debug!("Sudo check exit status: {:?}", sudo_check.status);
        
        if sudo_check.status.success() {
            info!("Sudo access available without password");
            return Ok(());
        }

        // Additional check: try to test route command access
        let route_check = Command::new("sudo")
            .args(&["-n", "route", "-n", "get", "default"])
            .output();
            
        if let Ok(route_output) = route_check {
            if route_output.status.success() {
                info!("Route command access verified");
                return Ok(());
            }
        }

        debug!("All privilege checks failed");
        Err(TunProxyError::RoutingError(
            "Insufficient privileges. Use request_macos_permissions() to trigger permission request.".to_string()
        ))
    }

    /// Request macOS permissions interactively
    #[cfg(target_os = "macos")]
    pub async fn request_macos_permissions() -> Result<bool> {
        use crate::macos_permissions::MacOSPermissions;
        MacOSPermissions::request_permissions_interactive().await
    }

    #[cfg(target_os = "linux")]
    fn check_linux_privileges() -> Result<()> {
        // Check if running as root
        let output = Command::new("id")
            .arg("-u")
            .output()
            .map_err(|e| TunProxyError::RoutingError(format!("Failed to check user ID: {}", e)))?;

        let uid_string = String::from_utf8_lossy(&output.stdout);
        let uid = uid_string.trim();
        
        if uid == "0" {
            info!("Running with root privileges");
            return Ok(());
        }

        // Check for CAP_NET_ADMIN capability
        let cap_check = Command::new("capsh")
            .args(&["--print"])
            .output();

        if let Ok(cap_output) = cap_check {
            let cap_str = String::from_utf8_lossy(&cap_output.stdout);
            if cap_str.contains("cap_net_admin") {
                info!("CAP_NET_ADMIN capability available");
                return Ok(());
            }
        }

        // Check sudo access
        let sudo_check = Command::new("sudo")
            .args(&["-n", "true"])
            .output()
            .map_err(|e| TunProxyError::RoutingError(format!("Failed to check sudo access: {}", e)))?;

        if sudo_check.status.success() {
            info!("Sudo access available without password");
            return Ok(());
        }

        Err(TunProxyError::RoutingError(
            "Insufficient privileges. Root access, CAP_NET_ADMIN capability, or passwordless sudo required.".to_string()
        ))
    }

    #[cfg(target_os = "windows")]
    fn check_windows_privileges() -> Result<()> {
        // Check if running as administrator
        let output = Command::new("net")
            .args(&["session"])
            .output()
            .map_err(|e| TunProxyError::RoutingError(format!("Failed to check admin privileges: {}", e)))?;

        if output.status.success() {
            info!("Running with administrator privileges");
            Ok(())
        } else {
            Err(TunProxyError::RoutingError(
                "Administrator privileges required.".to_string()
            ))
        }
    }

    #[cfg(target_os = "macos")]
    async fn execute_macos_command(cmd: &str) -> Result<()> {
        // Try to determine if we need sudo
        let needs_sudo = Self::command_needs_sudo(cmd);
        
        let output = if needs_sudo {
            // Check if running as root first
            let uid_output = Command::new("id")
                .arg("-u")
                .output()
                .map_err(|e| TunProxyError::RoutingError(format!("Failed to check user ID: {}", e)))?;
            
            let uid_string = String::from_utf8_lossy(&uid_output.stdout);
            let uid = uid_string.trim();
            
            if uid == "0" {
                // Already root, execute directly
                tokio::process::Command::new("sh")
                    .arg("-c")
                    .arg(cmd)
                    .output()
                    .await
            } else {
                // Use sudo
                tokio::process::Command::new("sudo")
                    .arg("sh")
                    .arg("-c")
                    .arg(cmd)
                    .output()
                    .await
            }
        } else {
            // Execute without sudo
            tokio::process::Command::new("sh")
                .arg("-c")
                .arg(cmd)
                .output()
                .await
        };

        let output = output
            .map_err(|e| TunProxyError::RoutingError(format!("Failed to execute command: {}", e)))?;

        if !output.status.success() {
            let stderr = String::from_utf8_lossy(&output.stderr);
            let stdout = String::from_utf8_lossy(&output.stdout);
            
            error!("Command failed: {}", cmd);
            error!("Stderr: {}", stderr);
            error!("Stdout: {}", stdout);
            
            // Provide helpful error messages
            if stderr.contains("must be root") || stderr.contains("Operation not permitted") {
                return Err(TunProxyError::RoutingError(
                    format!("Insufficient privileges to execute: {}\n\n{}", cmd, Self::get_privilege_instructions())
                ));
            }
            
            return Err(TunProxyError::RoutingError(format!("Command failed: {}", stderr)));
        }

        debug!("Command executed successfully: {}", cmd);
        Ok(())
    }

    #[cfg(target_os = "linux")]
    async fn execute_linux_command(cmd: &str) -> Result<()> {
        let needs_sudo = Self::command_needs_sudo(cmd);
        
        let output = if needs_sudo {
            let uid_output = Command::new("id")
                .arg("-u")
                .output()
                .map_err(|e| TunProxyError::RoutingError(format!("Failed to check user ID: {}", e)))?;
            
            let uid_string = String::from_utf8_lossy(&uid_output.stdout);
            let uid = uid_string.trim();
            
            if uid == "0" {
                tokio::process::Command::new("sh")
                    .arg("-c")
                    .arg(cmd)
                    .output()
                    .await
            } else {
                tokio::process::Command::new("sudo")
                    .arg("sh")
                    .arg("-c")
                    .arg(cmd)
                    .output()
                    .await
            }
        } else {
            tokio::process::Command::new("sh")
                .arg("-c")
                .arg(cmd)
                .output()
                .await
        };

        let output = output
            .map_err(|e| TunProxyError::RoutingError(format!("Failed to execute command: {}", e)))?;

        if !output.status.success() {
            let stderr = String::from_utf8_lossy(&output.stderr);
            
            if stderr.contains("Operation not permitted") || stderr.contains("Permission denied") {
                return Err(TunProxyError::RoutingError(
                    format!("Insufficient privileges to execute: {}\n\n{}", cmd, Self::get_privilege_instructions())
                ));
            }
            
            return Err(TunProxyError::RoutingError(format!("Command failed: {}", stderr)));
        }

        Ok(())
    }

    #[cfg(target_os = "windows")]
    async fn execute_windows_command(cmd: &str) -> Result<()> {
        let output = tokio::process::Command::new("cmd")
            .args(&["/C", cmd])
            .output()
            .await
            .map_err(|e| TunProxyError::RoutingError(format!("Failed to execute command: {}", e)))?;

        if !output.status.success() {
            let stderr = String::from_utf8_lossy(&output.stderr);
            
            if stderr.contains("Access is denied") {
                return Err(TunProxyError::RoutingError(
                    format!("Insufficient privileges to execute: {}\n\n{}", cmd, Self::get_privilege_instructions())
                ));
            }
            
            return Err(TunProxyError::RoutingError(format!("Command failed: {}", stderr)));
        }

        Ok(())
    }

    /// Determine if a command needs elevated privileges
    fn command_needs_sudo(cmd: &str) -> bool {
        let privileged_commands = [
            "route", "ip", "ifconfig", "netsh", "ipconfig",
            "iptables", "pfctl", "sysctl"
        ];
        
        privileged_commands.iter().any(|&priv_cmd| cmd.contains(priv_cmd))
    }

    /// Check if the application can create TUN devices
    pub fn check_tun_permissions() -> Result<()> {
        #[cfg(target_os = "macos")]
        {
            // On macOS, check if we can access /dev/tun*
            if std::path::Path::new("/dev/tun0").exists() || 
               std::path::Path::new("/dev/utun0").exists() {
                return Ok(());
            }
            
            // Check if we have permissions to create TUN devices
            Self::check_macos_privileges()
        }

        #[cfg(target_os = "linux")]
        {
            // Check if /dev/net/tun exists and is accessible
            if std::path::Path::new("/dev/net/tun").exists() {
                return Ok(());
            }
            
            Err(TunProxyError::TunError(
                "TUN device not available. Please ensure TUN/TAP support is enabled.".to_string()
            ))
        }

        #[cfg(target_os = "windows")]
        {
            // On Windows, TUN device creation requires admin privileges
            Self::check_windows_privileges()
        }

        #[cfg(not(any(target_os = "macos", target_os = "linux", target_os = "windows")))]
        {
            Err(TunProxyError::TunError("Unsupported platform".to_string()))
        }
    }
}