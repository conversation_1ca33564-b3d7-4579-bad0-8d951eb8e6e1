use crate::error::{Result, TunProxyError};
use std::process::Command;
use tracing::{debug, info, error, warn};

/// Privilege management for system operations
pub struct PrivilegeManager;

impl PrivilegeManager {
    /// Check if the application has the necessary privileges
    pub fn check_privileges() -> Result<()> {
        #[cfg(target_os = "macos")]
        {
            Self::check_macos_privileges()
        }

        #[cfg(target_os = "linux")]
        {
            Self::check_linux_privileges()
        }

        #[cfg(target_os = "windows")]
        {
            Self::check_windows_privileges()
        }

        #[cfg(not(any(target_os = "macos", target_os = "linux", target_os = "windows")))]
        {
            Err(TunProxyError::RoutingError("Unsupported platform".to_string()))
        }
    }

    /// Execute a command with elevated privileges if needed
    pub async fn execute_privileged_command(cmd: &str) -> Result<()> {
        debug!("Executing privileged command: {}", cmd);

        // Try to use cached credentials first
        if Self::try_cached_execution(cmd).await.is_ok() {
            debug!("Command executed successfully with cached credentials");
            return Ok(());
        }

        #[cfg(target_os = "macos")]
        {
            Self::execute_macos_command(cmd).await
        }

        #[cfg(target_os = "linux")]
        {
            Self::execute_linux_command(cmd).await
        }

        #[cfg(target_os = "windows")]
        {
            Self::execute_windows_command(cmd).await
        }

        #[cfg(not(any(target_os = "macos", target_os = "linux", target_os = "windows")))]
        {
            Err(TunProxyError::RoutingError("Unsupported platform".to_string()))
        }
    }

    /// Try to execute command with cached credentials (without password prompt)
    async fn try_cached_execution(cmd: &str) -> Result<()> {
        #[cfg(target_os = "macos")]
        {
            // Try to execute with sudo -n (non-interactive)
            let output = tokio::process::Command::new("sudo")
                .arg("-n") // Non-interactive mode
                .arg("sh")
                .arg("-c")
                .arg(cmd)
                .output()
                .await;

            match output {
                Ok(result) if result.status.success() => {
                    debug!("Command executed successfully with cached sudo credentials");
                    Ok(())
                }
                _ => {
                    debug!("Cached credentials not available or expired");
                    Err(TunProxyError::RoutingError("Cached credentials not available".to_string()))
                }
            }
        }

        #[cfg(not(target_os = "macos"))]
        {
            // For other platforms, always require fresh authentication
            Err(TunProxyError::RoutingError("Cached credentials not supported on this platform".to_string()))
        }
    }

    /// Execute multiple privileged commands in a single batch to minimize password prompts
    pub async fn execute_privileged_commands_batch(commands: &[String]) -> Result<()> {
        if commands.is_empty() {
            return Ok(());
        }

        info!("Executing {} commands in batch to minimize password prompts", commands.len());

        // Join commands with && to execute them in sequence
        let batch_cmd = commands.join(" && ");
        Self::execute_privileged_command(&batch_cmd).await
    }

    /// Install a privilege helper to avoid repeated password prompts
    pub async fn install_privilege_helper() -> Result<()> {
        #[cfg(target_os = "macos")]
        {
            info!("Installing privilege helper to minimize future password prompts");

            // Create a sudoers entry for TUN Proxy commands
            let sudoers_content = format!(
                "# TUN Proxy privilege helper\n\
                %admin ALL=(ALL) NOPASSWD: /sbin/ifconfig utun* *\n\
                %admin ALL=(ALL) NOPASSWD: /sbin/route add *\n\
                %admin ALL=(ALL) NOPASSWD: /sbin/route delete *\n\
                # End TUN Proxy privilege helper\n"
            );

            let temp_file = "/tmp/tun_proxy_sudoers";

            // Write sudoers content to temp file
            if let Err(e) = tokio::fs::write(temp_file, &sudoers_content).await {
                warn!("Failed to write sudoers file: {}", e);
                return Err(TunProxyError::RoutingError(format!("Failed to create sudoers file: {}", e)));
            }

            // Install the sudoers file
            let install_cmd = format!(
                "visudo -c -f {} && cp {} /etc/sudoers.d/tun_proxy && chmod 440 /etc/sudoers.d/tun_proxy",
                temp_file, temp_file
            );

            match Self::execute_macos_command(&install_cmd).await {
                Ok(()) => {
                    info!("Privilege helper installed successfully");
                    // Clean up temp file
                    let _ = tokio::fs::remove_file(temp_file).await;
                    Ok(())
                }
                Err(e) => {
                    warn!("Failed to install privilege helper: {}", e);
                    // Clean up temp file
                    let _ = tokio::fs::remove_file(temp_file).await;
                    Err(TunProxyError::RoutingError(format!("Failed to install privilege helper: {}", e)))
                }
            }
        }

        #[cfg(not(target_os = "macos"))]
        {
            Err(TunProxyError::RoutingError("Privilege helper not supported on this platform".to_string()))
        }
    }

    /// Remove the privilege helper
    pub async fn remove_privilege_helper() -> Result<()> {
        #[cfg(target_os = "macos")]
        {
            info!("Removing privilege helper");

            let remove_cmd = "rm -f /etc/sudoers.d/tun_proxy";
            match Self::execute_macos_command(remove_cmd).await {
                Ok(()) => {
                    info!("Privilege helper removed successfully");
                    Ok(())
                }
                Err(e) => {
                    warn!("Failed to remove privilege helper: {}", e);
                    Err(TunProxyError::RoutingError(format!("Failed to remove privilege helper: {}", e)))
                }
            }
        }

        #[cfg(not(target_os = "macos"))]
        {
            Err(TunProxyError::RoutingError("Privilege helper not supported on this platform".to_string()))
        }
    }

    /// Check if privilege helper is installed
    pub async fn is_privilege_helper_installed() -> bool {
        #[cfg(target_os = "macos")]
        {
            tokio::fs::metadata("/etc/sudoers.d/tun_proxy").await.is_ok()
        }

        #[cfg(not(target_os = "macos"))]
        {
            false
        }
    }

    /// Get instructions for running with proper privileges
    pub fn get_privilege_instructions() -> String {
        #[cfg(target_os = "macos")]
        {
            "On macOS, TUN Proxy requires administrator privileges to create TUN devices and modify routing tables.\n\n\
            To resolve this issue:\n\
            1. Click 'Grant Permissions' to use the built-in permission helper\n\
            2. Or run the application with elevated privileges: sudo npm run tauri dev\n\
            3. For production builds: sudo ./tun-proxy\n\n\
            Note: TUN device creation requires system-level network access which is restricted on macOS for security reasons.".to_string()
        }

        #[cfg(target_os = "linux")]
        {
            "On Linux, TUN Proxy requires root privileges or CAP_NET_ADMIN capability.\n\n\
            Please run with one of the following options:\n\
            1. Run: sudo ./tun-proxy\n\
            2. Or grant CAP_NET_ADMIN: sudo setcap cap_net_admin+ep ./tun-proxy\n\
            3. For development: sudo npm run tauri dev".to_string()
        }

        #[cfg(target_os = "windows")]
        {
            "On Windows, TUN Proxy requires administrator privileges.\n\n\
            Please run the application as Administrator:\n\
            1. Right-click the application and select 'Run as administrator'\n\
            2. Or run from an elevated command prompt".to_string()
        }

        #[cfg(not(any(target_os = "macos", target_os = "linux", target_os = "windows")))]
        {
            "This platform is not supported.".to_string()
        }
    }

    #[cfg(target_os = "macos")]
    fn check_macos_privileges() -> Result<()> {
        debug!("Checking macOS privileges...");

        // Check if running as root
        let output = Command::new("id")
            .arg("-u")
            .output()
            .map_err(|e| TunProxyError::RoutingError(format!("Failed to check user ID: {}", e)))?;

        let uid_string = String::from_utf8_lossy(&output.stdout);
        let uid = uid_string.trim();

        debug!("Current UID: {}", uid);

        if uid == "0" {
            info!("Running with root privileges");
            return Ok(());
        }

        // Check if we can use sudo without password
        let sudo_check = Command::new("sudo")
            .args(&["-n", "true"])
            .output()
            .map_err(|e| TunProxyError::RoutingError(format!("Failed to check sudo access: {}", e)))?;

        debug!("Sudo check exit status: {:?}", sudo_check.status);

        if sudo_check.status.success() {
            info!("Sudo access available without password");
            return Ok(());
        }

        // Check if permissions were previously requested
        if std::path::Path::new("/tmp/.tun_proxy_permissions_requested").exists() {
            debug!("Permission request flag found, checking TUN capabilities");

            // Test if we can at least read TUN device information
            let tun_check = Command::new("ifconfig")
                .arg("-l")
                .output();

            if let Ok(tun_output) = tun_check {
                if tun_output.status.success() {
                    let interfaces = String::from_utf8_lossy(&tun_output.stdout);
                    debug!("Available interfaces: {}", interfaces);

                    // If we can list interfaces, we have some network access
                    info!("Network interface access available");
                    return Ok(());
                }
            }
        }

        // Additional check: try to test route command access
        let route_check = Command::new("sudo")
            .args(&["-n", "route", "-n", "get", "default"])
            .output();

        if let Ok(route_output) = route_check {
            if route_output.status.success() {
                info!("Route command access verified");
                return Ok(());
            }
        }

        debug!("All privilege checks failed");
        Err(TunProxyError::RoutingError(
            "Insufficient privileges. Use request_macos_permissions() to trigger permission request.".to_string()
        ))
    }

    /// Request macOS permissions interactively
    #[cfg(target_os = "macos")]
    pub async fn request_macos_permissions() -> Result<bool> {
        use crate::macos_permissions::MacOSPermissions;
        MacOSPermissions::request_permissions_interactive().await
    }

    #[cfg(target_os = "linux")]
    fn check_linux_privileges() -> Result<()> {
        // Check if running as root
        let output = Command::new("id")
            .arg("-u")
            .output()
            .map_err(|e| TunProxyError::RoutingError(format!("Failed to check user ID: {}", e)))?;

        let uid_string = String::from_utf8_lossy(&output.stdout);
        let uid = uid_string.trim();
        
        if uid == "0" {
            info!("Running with root privileges");
            return Ok(());
        }

        // Check for CAP_NET_ADMIN capability
        let cap_check = Command::new("capsh")
            .args(&["--print"])
            .output();

        if let Ok(cap_output) = cap_check {
            let cap_str = String::from_utf8_lossy(&cap_output.stdout);
            if cap_str.contains("cap_net_admin") {
                info!("CAP_NET_ADMIN capability available");
                return Ok(());
            }
        }

        // Check sudo access
        let sudo_check = Command::new("sudo")
            .args(&["-n", "true"])
            .output()
            .map_err(|e| TunProxyError::RoutingError(format!("Failed to check sudo access: {}", e)))?;

        if sudo_check.status.success() {
            info!("Sudo access available without password");
            return Ok(());
        }

        Err(TunProxyError::RoutingError(
            "Insufficient privileges. Root access, CAP_NET_ADMIN capability, or passwordless sudo required.".to_string()
        ))
    }

    #[cfg(target_os = "windows")]
    fn check_windows_privileges() -> Result<()> {
        // Check if running as administrator
        let output = Command::new("net")
            .args(&["session"])
            .output()
            .map_err(|e| TunProxyError::RoutingError(format!("Failed to check admin privileges: {}", e)))?;

        if output.status.success() {
            info!("Running with administrator privileges");
            Ok(())
        } else {
            Err(TunProxyError::RoutingError(
                "Administrator privileges required.".to_string()
            ))
        }
    }

    #[cfg(target_os = "macos")]
    async fn execute_macos_command(cmd: &str) -> Result<()> {
        // Try to determine if we need sudo
        let needs_sudo = Self::command_needs_sudo(cmd);

        let output = if needs_sudo {
            // Check if running as root first
            let uid_output = Command::new("id")
                .arg("-u")
                .output()
                .map_err(|e| TunProxyError::RoutingError(format!("Failed to check user ID: {}", e)))?;

            let uid_string = String::from_utf8_lossy(&uid_output.stdout);
            let uid = uid_string.trim();

            if uid == "0" {
                // Already root, execute directly
                tokio::process::Command::new("sh")
                    .arg("-c")
                    .arg(cmd)
                    .output()
                    .await
            } else {
                // Use osascript to request admin privileges for GUI applications
                debug!("Using osascript to execute privileged command in GUI context");

                let script = format!(
                    r#"do shell script "{}" with administrator privileges"#,
                    cmd.replace("\"", "\\\"")
                );

                tokio::process::Command::new("osascript")
                    .arg("-e")
                    .arg(&script)
                    .output()
                    .await
            }
        } else {
            // Execute without sudo
            tokio::process::Command::new("sh")
                .arg("-c")
                .arg(cmd)
                .output()
                .await
        };

        let output = output
            .map_err(|e| TunProxyError::RoutingError(format!("Failed to execute command: {}", e)))?;

        if !output.status.success() {
            let stderr = String::from_utf8_lossy(&output.stderr);
            let stdout = String::from_utf8_lossy(&output.stdout);
            
            error!("Command failed: {}", cmd);
            error!("Stderr: {}", stderr);
            error!("Stdout: {}", stdout);
            
            // Provide helpful error messages
            if stderr.contains("must be root") || stderr.contains("Operation not permitted") {
                return Err(TunProxyError::RoutingError(
                    format!("Insufficient privileges to execute: {}\n\n{}", cmd, Self::get_privilege_instructions())
                ));
            }
            
            return Err(TunProxyError::RoutingError(format!("Command failed: {}", stderr)));
        }

        debug!("Command executed successfully: {}", cmd);
        Ok(())
    }

    #[cfg(target_os = "linux")]
    async fn execute_linux_command(cmd: &str) -> Result<()> {
        let needs_sudo = Self::command_needs_sudo(cmd);
        
        let output = if needs_sudo {
            let uid_output = Command::new("id")
                .arg("-u")
                .output()
                .map_err(|e| TunProxyError::RoutingError(format!("Failed to check user ID: {}", e)))?;
            
            let uid_string = String::from_utf8_lossy(&uid_output.stdout);
            let uid = uid_string.trim();
            
            if uid == "0" {
                tokio::process::Command::new("sh")
                    .arg("-c")
                    .arg(cmd)
                    .output()
                    .await
            } else {
                tokio::process::Command::new("sudo")
                    .arg("sh")
                    .arg("-c")
                    .arg(cmd)
                    .output()
                    .await
            }
        } else {
            tokio::process::Command::new("sh")
                .arg("-c")
                .arg(cmd)
                .output()
                .await
        };

        let output = output
            .map_err(|e| TunProxyError::RoutingError(format!("Failed to execute command: {}", e)))?;

        if !output.status.success() {
            let stderr = String::from_utf8_lossy(&output.stderr);
            
            if stderr.contains("Operation not permitted") || stderr.contains("Permission denied") {
                return Err(TunProxyError::RoutingError(
                    format!("Insufficient privileges to execute: {}\n\n{}", cmd, Self::get_privilege_instructions())
                ));
            }
            
            return Err(TunProxyError::RoutingError(format!("Command failed: {}", stderr)));
        }

        Ok(())
    }

    #[cfg(target_os = "windows")]
    async fn execute_windows_command(cmd: &str) -> Result<()> {
        let output = tokio::process::Command::new("cmd")
            .args(&["/C", cmd])
            .output()
            .await
            .map_err(|e| TunProxyError::RoutingError(format!("Failed to execute command: {}", e)))?;

        if !output.status.success() {
            let stderr = String::from_utf8_lossy(&output.stderr);
            
            if stderr.contains("Access is denied") {
                return Err(TunProxyError::RoutingError(
                    format!("Insufficient privileges to execute: {}\n\n{}", cmd, Self::get_privilege_instructions())
                ));
            }
            
            return Err(TunProxyError::RoutingError(format!("Command failed: {}", stderr)));
        }

        Ok(())
    }

    /// Determine if a command needs elevated privileges
    fn command_needs_sudo(cmd: &str) -> bool {
        let privileged_commands = [
            "route", "ip", "ifconfig", "netsh", "ipconfig",
            "iptables", "pfctl", "sysctl"
        ];
        
        privileged_commands.iter().any(|&priv_cmd| cmd.contains(priv_cmd))
    }

    /// Check if the application can create TUN devices
    pub fn check_tun_permissions() -> Result<()> {
        #[cfg(target_os = "macos")]
        {
            // On macOS, check if we can access existing TUN devices
            if std::path::Path::new("/dev/tun0").exists() ||
               std::path::Path::new("/dev/utun0").exists() {
                debug!("Found existing TUN devices");
                return Ok(());
            }

            // Check if we can list network interfaces (basic network access)
            let ifconfig_check = Command::new("ifconfig")
                .arg("-l")
                .output();

            if let Ok(output) = ifconfig_check {
                if output.status.success() {
                    let interfaces = String::from_utf8_lossy(&output.stdout);
                    debug!("Network interfaces available: {}", interfaces);

                    // If we can list interfaces and have permission flag, assume TUN access
                    if std::path::Path::new("/tmp/.tun_proxy_permissions_requested").exists() {
                        info!("TUN permissions likely available (permission flag exists)");
                        return Ok(());
                    }
                }
            }

            // Fall back to general privilege check
            Self::check_macos_privileges()
        }

        #[cfg(target_os = "linux")]
        {
            // Check if /dev/net/tun exists and is accessible
            if std::path::Path::new("/dev/net/tun").exists() {
                return Ok(());
            }
            
            Err(TunProxyError::TunError(
                "TUN device not available. Please ensure TUN/TAP support is enabled.".to_string()
            ))
        }

        #[cfg(target_os = "windows")]
        {
            // On Windows, TUN device creation requires admin privileges
            Self::check_windows_privileges()
        }

        #[cfg(not(any(target_os = "macos", target_os = "linux", target_os = "windows")))]
        {
            Err(TunProxyError::TunError("Unsupported platform".to_string()))
        }
    }
}