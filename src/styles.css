@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  * {
    @apply border-gray-700;
  }
  
  body {
    @apply bg-gray-900 text-gray-100;
  }
}

@layer components {
  .btn {
    @apply px-4 py-2 rounded-lg font-medium transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-gray-900;
  }
  
  .btn-primary {
    @apply btn bg-primary-600 hover:bg-primary-700 text-white focus:ring-primary-500;
  }
  
  .btn-secondary {
    @apply btn bg-gray-700 hover:bg-gray-600 text-gray-100 focus:ring-gray-500;
  }
  
  .btn-danger {
    @apply btn bg-red-600 hover:bg-red-700 text-white focus:ring-red-500;
  }
  
  .card {
    @apply bg-gray-800 rounded-xl border border-gray-700 shadow-lg;
  }
  
  .input {
    @apply bg-gray-800 border border-gray-600 rounded-lg px-3 py-2 text-gray-100 placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent;
  }
  
  .select {
    @apply input appearance-none cursor-pointer;
  }
}

@layer utilities {
  .animate-slide-up {
    animation: slideUp 0.3s ease-out;
  }
}

@keyframes slideUp {
  from {
    transform: translateY(100%);
  }
  to {
    transform: translateY(0);
  }
}