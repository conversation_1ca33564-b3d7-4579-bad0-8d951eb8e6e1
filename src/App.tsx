import { useState, useEffect } from 'react';
import { invoke } from '@tauri-apps/api/tauri';
import ConnectionControl from './components/ConnectionControl';
import StatusDisplay from './components/StatusDisplay';
import ModeSelector from './components/ModeSelector';
import NodeSelector from './components/NodeSelector';
import Speed<PERSON>hart from './components/SpeedChart';
import LogViewer from './components/LogViewer';
import PrivilegeWarning from './components/PrivilegeWarning';
import ErrorModal from './components/ErrorModal';
import { ConnectionInfo, NetworkStats, LogEntry } from './types';

function App() {
  const [connectionInfo, setConnectionInfo] = useState<ConnectionInfo | null>(null);
  const [proxyMode, setProxyMode] = useState<string>('Rule');
  const [selectedNode, setSelectedNode] = useState<string | null>(null);
  const [availableNodes, setAvailableNodes] = useState<string[]>([]);
  const [networkStats, setNetworkStats] = useState<NetworkStats | null>(null);
  const [logs, setLogs] = useState<LogEntry[]>([]);
  const [showLogs, setShowLogs] = useState(false);
  const [loading, setLoading] = useState(true);
  const [showErrorModal, setShowErrorModal] = useState(false);
  const [errorDetails, setErrorDetails] = useState({ title: '', message: '' });

  // Fetch initial data
  useEffect(() => {
    const initializeApp = async () => {
      try {
        const [status, mode, node, nodes] = await Promise.all([
          invoke<ConnectionInfo>('get_connection_status'),
          invoke<string>('get_proxy_mode'),
          invoke<string | null>('get_selected_node'),
          invoke<string[]>('get_available_nodes'),
        ]);

        setConnectionInfo(status);
        setProxyMode(mode);
        setSelectedNode(node);
        setAvailableNodes(nodes);
      } catch (error) {
        console.error('Failed to initialize app:', error);
      } finally {
        setLoading(false);
      }
    };

    initializeApp();
  }, []);

  // Update network stats and logs periodically
  useEffect(() => {
    const interval = setInterval(async () => {
      try {
        const [stats, newLogs, status] = await Promise.all([
          invoke<NetworkStats>('get_network_stats'),
          invoke<LogEntry[]>('get_logs'),
          invoke<ConnectionInfo>('get_connection_status'),
        ]);

        setNetworkStats(stats);
        setLogs(newLogs);
        setConnectionInfo(status);
      } catch (error) {
        console.error('Failed to update stats:', error);
      }
    }, 1000);

    return () => clearInterval(interval);
  }, []);

  const handleConnect = async () => {
    try {
      const result = await invoke<ConnectionInfo>('connect_proxy');
      setConnectionInfo(result);
    } catch (error) {
      console.error('Failed to connect:', error);
      setErrorDetails({
        title: 'Connection Failed',
        message: typeof error === 'string' ? error : 'An unknown error occurred'
      });
      setShowErrorModal(true);
    }
  };

  const handleDisconnect = async () => {
    try {
      const result = await invoke<ConnectionInfo>('disconnect_proxy');
      setConnectionInfo(result);
    } catch (error) {
      console.error('Failed to disconnect:', error);
    }
  };

  const handleModeChange = async (mode: string) => {
    try {
      await invoke('set_proxy_mode', { mode });
      setProxyMode(mode);
    } catch (error) {
      console.error('Failed to change mode:', error);
    }
  };

  const handleNodeChange = async (node: string | null) => {
    try {
      await invoke('set_selected_node', { nodeName: node });
      setSelectedNode(node);
    } catch (error) {
      console.error('Failed to change node:', error);
    }
  };

  const handleClearLogs = async () => {
    try {
      await invoke('clear_logs');
      setLogs([]);
    } catch (error) {
      console.error('Failed to clear logs:', error);
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-900 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-500 mx-auto mb-4"></div>
          <p className="text-gray-400">Loading TUN Proxy...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-900 text-gray-100">
      <div className="container mx-auto px-6 py-8 max-w-4xl">
        {/* Header */}
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold text-white mb-2">TUN Proxy</h1>
          <p className="text-gray-400">Cross-platform network proxy with TUN interface</p>
        </div>

        {/* Privilege Warning */}
        <PrivilegeWarning />

        {/* Main Dashboard */}
        <div className="grid gap-6">
          {/* Status and Connection Control */}
          <div className="card p-6">
            <StatusDisplay 
              connectionInfo={connectionInfo}
              proxyMode={proxyMode}
              selectedNode={selectedNode}
            />
            
            <div className="mt-6">
              <ConnectionControl
                connectionInfo={connectionInfo}
                onConnect={handleConnect}
                onDisconnect={handleDisconnect}
              />
            </div>
          </div>

          {/* Mode and Node Selection */}
          <div className="grid md:grid-cols-2 gap-6">
            <div className="card p-6">
              <h3 className="text-lg font-semibold mb-4">Proxy Mode</h3>
              <ModeSelector
                currentMode={proxyMode}
                onModeChange={handleModeChange}
              />
            </div>

            <div className="card p-6">
              <h3 className="text-lg font-semibold mb-4">Node Selection</h3>
              <NodeSelector
                selectedNode={selectedNode}
                availableNodes={availableNodes}
                disabled={proxyMode === 'Direct'}
                onNodeChange={handleNodeChange}
              />
            </div>
          </div>

          {/* Speed Chart */}
          <div className="card p-6">
            <h3 className="text-lg font-semibold mb-4">Network Activity</h3>
            <SpeedChart networkStats={networkStats} />
          </div>
        </div>

        {/* Log Viewer Toggle */}
        <div className="fixed bottom-4 right-4">
          <button
            onClick={() => setShowLogs(!showLogs)}
            className="btn-secondary p-3 rounded-full shadow-lg"
            title="Toggle Logs"
          >
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
            </svg>
          </button>
        </div>

        {/* Log Viewer */}
        {showLogs && (
          <LogViewer
            logs={logs}
            onClose={() => setShowLogs(false)}
            onClear={handleClearLogs}
          />
        )}

        {/* Error Modal */}
        <ErrorModal
          isOpen={showErrorModal}
          onClose={() => setShowErrorModal(false)}
          title={errorDetails.title}
          message={errorDetails.message}
        />
      </div>
    </div>
  );
}

export default App;