use std::net::IpAddr;
use ipnet::IpNet;
use crate::config::{Rule, RuleType, Policy, AppConfig};
use crate::core::ConnectionRequest;
use tracing::{debug, trace};

/// Operating modes for the proxy
#[derive(Debug, <PERSON>lone, PartialEq)]
pub enum ProxyMode {
    /// Route all traffic through the selected node
    Global,
    /// Use rule-based routing
    Rule,
    /// Direct connection for all traffic
    Direct,
}

/// Rule engine for determining how to handle connections
pub struct RuleEngine {
    config: Option<AppConfig>,
    mode: ProxyMode,
    selected_node: Option<String>,
}

impl RuleEngine {
    pub fn new() -> Self {
        Self {
            config: None,
            mode: ProxyMode::Direct,
            selected_node: None,
        }
    }

    /// Update the configuration
    pub fn set_config(&mut self, config: AppConfig) {
        self.config = Some(config);
    }

    /// Set the operating mode
    pub fn set_mode(&mut self, mode: ProxyMode) {
        self.mode = mode.clone();
        debug!("Rule engine mode changed to {:?}", mode);
    }

    /// Set the selected node for Global mode
    pub fn set_selected_node(&mut self, node_name: Option<String>) {
        self.selected_node = node_name;
        debug!("Selected node changed to {:?}", self.selected_node);
    }

    /// Get the current mode
    pub fn get_mode(&self) -> &ProxyMode {
        &self.mode
    }

    /// Get the selected node
    pub fn get_selected_node(&self) -> Option<&String> {
        self.selected_node.as_ref()
    }

    /// Determine the policy for a connection request
    pub fn evaluate_request(&self, request: &ConnectionRequest) -> Policy {
        match self.mode {
            ProxyMode::Direct => {
                trace!("Direct mode: allowing direct connection to {}", request.destination);
                Policy::Direct
            }
            ProxyMode::Global => {
                if let Some(ref node) = self.selected_node {
                    trace!("Global mode: routing {} through {}", request.destination, node);
                    Policy::Node(node.clone())
                } else {
                    debug!("Global mode but no node selected, falling back to direct");
                    Policy::Direct
                }
            }
            ProxyMode::Rule => {
                self.evaluate_rules(request)
            }
        }
    }

    /// Evaluate rules against a connection request
    fn evaluate_rules(&self, request: &ConnectionRequest) -> Policy {
        let config = match &self.config {
            Some(config) => config,
            None => {
                debug!("No configuration available, using direct connection");
                return Policy::Direct;
            }
        };

        // Extract destination information
        let dest_ip = request.destination.ip();
        let dest_host = self.ip_to_hostname(&dest_ip); // In real implementation, this would do reverse DNS

        trace!("Evaluating rules for destination: {} ({})", request.destination, dest_host.as_deref().unwrap_or("unknown"));

        // Check each rule in order
        for rule in &config.rules {
            if self.rule_matches(rule, &dest_ip, dest_host.as_deref()) {
                debug!("Rule matched: {:?} -> {:?}", rule, rule.policy);
                return rule.policy.clone();
            }
        }

        // No rule matched, use default policy
        debug!("No rule matched, using default policy: {:?}", config.default_policy);
        config.default_policy.clone()
    }

    /// Check if a rule matches the destination
    fn rule_matches(&self, rule: &Rule, dest_ip: &IpAddr, dest_host: Option<&str>) -> bool {
        match rule.rule_type {
            RuleType::IpCidr => {
                if let Ok(network) = rule.value.parse::<IpNet>() {
                    network.contains(dest_ip)
                } else {
                    debug!("Invalid IP CIDR in rule: {}", rule.value);
                    false
                }
            }
            RuleType::Domain => {
                if let Some(host) = dest_host {
                    host.eq_ignore_ascii_case(&rule.value)
                } else {
                    false
                }
            }
            RuleType::DomainSuffix => {
                if let Some(host) = dest_host {
                    host.to_lowercase().ends_with(&rule.value.to_lowercase()) ||
                    host.to_lowercase().ends_with(&format!(".{}", rule.value.to_lowercase()))
                } else {
                    false
                }
            }
            RuleType::DomainKeyword => {
                if let Some(host) = dest_host {
                    host.to_lowercase().contains(&rule.value.to_lowercase())
                } else {
                    false
                }
            }
        }
    }

    /// Convert IP to hostname (placeholder implementation)
    /// In a real implementation, this would perform reverse DNS lookup
    /// or maintain a cache of known IP-to-hostname mappings
    fn ip_to_hostname(&self, _ip: &IpAddr) -> Option<String> {
        // Placeholder: In a real implementation, you would:
        // 1. Check a local cache of IP -> hostname mappings
        // 2. Perform reverse DNS lookup if not cached
        // 3. Extract hostname from SNI if available
        // 4. Use DPI (Deep Packet Inspection) techniques if needed
        
        // For now, return None to indicate hostname is unknown
        None
    }
}

impl Default for RuleEngine {
    fn default() -> Self {
        Self::new()
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use std::{net::SocketAddr, str::FromStr};

    #[test]
    fn test_ip_cidr_rule() {
        let mut engine = RuleEngine::new();
        let config = AppConfig {
            node_config: "".to_string(),
            rules: vec![
                Rule {
                    rule_type: RuleType::IpCidr,
                    value: "***********/16".to_string(),
                    policy: Policy::Direct,
                }
            ],
            default_policy: Policy::Node("default".to_string()),
        };
        engine.set_config(config);
        engine.set_mode(ProxyMode::Rule);

        let request = ConnectionRequest {
            source: SocketAddr::from_str("********:12345").unwrap(),
            destination: SocketAddr::from_str("*************:80").unwrap(),
        };

        match engine.evaluate_request(&request) {
            Policy::Direct => assert!(true),
            _ => assert!(false, "Expected Direct policy for private IP"),
        }
    }

    #[test]
    fn test_global_mode() {
        let mut engine = RuleEngine::new();
        engine.set_mode(ProxyMode::Global);
        engine.set_selected_node(Some("test-node".to_string()));

        let request = ConnectionRequest {
            source: SocketAddr::from_str("********:12345").unwrap(),
            destination: SocketAddr::from_str("*******:53").unwrap(),
        };

        match engine.evaluate_request(&request) {
            Policy::Node(node) => assert_eq!(node, "test-node"),
            _ => assert!(false, "Expected Node policy in Global mode"),
        }
    }
}