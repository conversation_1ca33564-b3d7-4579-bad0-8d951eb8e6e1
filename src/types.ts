export interface ConnectionInfo {
  status: ConnectionStatus;
  duration_seconds?: number;
}

export type ConnectionStatus = 
  | 'Disconnected'
  | 'Connecting'
  | { Connected: { since: string } }
  | { Error: { message: string } };

export interface NetworkStats {
  bytes_sent: number;
  bytes_received: number;
  packets_sent: number;
  packets_received: number;
  connections_active: number;
  connections_total: number;
  upload_speed: number;
  download_speed: number;
}

export interface LogEntry {
  timestamp: string;
  level: string;
  message: string;
}

export type ProxyMode = 'Global' | 'Rule' | 'Direct';

export interface SpeedDataPoint {
  timestamp: number;
  upload: number;
  download: number;
}