use thiserror::Error;

#[derive(Error, Debug)]
pub enum TunProxyError {
    #[error("Network error: {0}")]
    NetworkError(#[from] std::io::Error),
    
    #[error("Configuration error: {0}")]
    ConfigError(String),
    
    #[error("TUN device error: {0}")]
    TunError(String),
    
    #[error("Routing error: {0}")]
    RoutingError(String),
    
    #[error("Proxy error: {0}")]
    ProxyError(#[from] ProxyError),
    
    #[error("Encryption error: {0}")]
    EncryptionError(String),
    
    #[error("HTTP error: {0}")]
    HttpError(#[from] reqwest::Error),
    
    #[error("Serialization error: {0}")]
    SerializationError(#[from] serde_json::Error),
}

#[derive(Debug, Clone)]
pub enum ProxyError {
    NetworkError,
    AuthenticationFailed,
    NodeNotFound,
    ConnectionRefused,
}

impl std::fmt::Display for ProxyError {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            ProxyError::NetworkError => write!(f, "Network error"),
            ProxyError::AuthenticationFailed => write!(f, "Authentication failed"),
            ProxyError::NodeNotFound => write!(f, "Node not found"),
            ProxyError::ConnectionRefused => write!(f, "Connection refused"),
        }
    }
}

impl std::error::Error for ProxyError {}

pub type Result<T> = std::result::Result<T, TunProxyError>;