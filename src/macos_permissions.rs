use crate::error::{Result, TunProxyError};
use std::process::Command;
use tracing::{debug, info, warn, error};

/// macOS specific permission management
pub struct MacOSPermissions;

impl MacOSPermissions {
    /// Request administrator privileges using macOS authorization framework
    pub async fn request_admin_privileges() -> Result<()> {
        info!("Requesting administrator privileges on macOS");
        
        // Use osascript to trigger the system authorization dialog
        let script = r#"
        do shell script "echo 'TUN Proxy requires administrator privileges to create network interfaces and modify routing tables.'" with administrator privileges
        "#;
        
        let output = tokio::process::Command::new("osascript")
            .arg("-e")
            .arg(script)
            .output()
            .await
            .map_err(|e| TunProxyError::RoutingError(format!("Failed to request privileges: {}", e)))?;
        
        if output.status.success() {
            info!("Administrator privileges granted");
            Ok(())
        } else {
            let stderr = String::from_utf8_lossy(&output.stderr);
            warn!("User denied administrator privileges: {}", stderr);
            Err(TunProxyError::RoutingError(
                "Administrator privileges are required but were denied by user".to_string()
            ))
        }
    }
    
    /// Request network configuration access through System Preferences
    pub async fn request_network_access() -> Result<()> {
        info!("Opening Network preferences to request access");
        
        // Open System Preferences to Network section
        let output = tokio::process::Command::new("open")
            .arg("-b")
            .arg("com.apple.preference.network")
            .output()
            .await
            .map_err(|e| TunProxyError::RoutingError(format!("Failed to open Network preferences: {}", e)))?;
        
        if output.status.success() {
            info!("Network preferences opened");
            Ok(())
        } else {
            let stderr = String::from_utf8_lossy(&output.stderr);
            Err(TunProxyError::RoutingError(format!("Failed to open Network preferences: {}", stderr)))
        }
    }
    
    /// Request Security & Privacy preferences access
    pub async fn request_security_privacy_access() -> Result<()> {
        info!("Opening Security & Privacy preferences");
        
        // Open System Preferences to Security & Privacy section
        let output = tokio::process::Command::new("open")
            .arg("-b")
            .arg("com.apple.preference.security")
            .arg("Privacy_AllFiles")
            .output()
            .await
            .map_err(|e| TunProxyError::RoutingError(format!("Failed to open Security preferences: {}", e)))?;
        
        if output.status.success() {
            info!("Security & Privacy preferences opened");
            Ok(())
        } else {
            let stderr = String::from_utf8_lossy(&output.stderr);
            Err(TunProxyError::RoutingError(format!("Failed to open Security preferences: {}", stderr)))
        }
    }
    
    /// Check if we have TUN/utun access
    pub fn check_tun_access() -> Result<bool> {
        // Check if we can access /dev/utun or create utun interfaces
        let utun_check = Command::new("ifconfig")
            .arg("-l")
            .output()
            .map_err(|e| TunProxyError::TunError(format!("Failed to check interfaces: {}", e)))?;
        
        let interfaces = String::from_utf8_lossy(&utun_check.stdout);
        let has_utun = interfaces.contains("utun");
        
        debug!("TUN access check - has utun interfaces: {}", has_utun);
        Ok(has_utun)
    }
    
    /// Comprehensive permission request flow
    pub async fn request_all_permissions() -> Result<()> {
        info!("Starting comprehensive permission request flow for macOS");
        
        // Step 1: Request administrator privileges first
        match Self::request_admin_privileges().await {
            Ok(()) => {
                info!("Administrator privileges obtained");
            }
            Err(e) => {
                warn!("Failed to get administrator privileges: {}", e);
                // Continue with other permission requests
            }
        }
        
        // Step 2: Check if we need to open Security & Privacy
        if !Self::check_current_privileges()? {
            info!("Opening Security & Privacy preferences for additional permissions");
            Self::request_security_privacy_access().await?;
            
            // Give user time to configure
            tokio::time::sleep(tokio::time::Duration::from_secs(2)).await;
        }
        
        // Step 3: Check TUN access
        if !Self::check_tun_access()? {
            info!("TUN access may be limited, opening Network preferences");
            Self::request_network_access().await?;
        }
        
        Ok(())
    }
    
    /// Check current privilege level
    pub fn check_current_privileges() -> Result<bool> {
        let output = Command::new("id")
            .arg("-u")
            .output()
            .map_err(|e| TunProxyError::RoutingError(format!("Failed to check user ID: {}", e)))?;
        
        let uid_string = String::from_utf8_lossy(&output.stdout);
        let uid = uid_string.trim();
        
        if uid == "0" {
            return Ok(true);
        }
        
        // Check if we can use sudo without password
        let sudo_check = Command::new("sudo")
            .args(&["-n", "true"])
            .output()
            .map_err(|e| TunProxyError::RoutingError(format!("Failed to check sudo access: {}", e)))?;
        
        Ok(sudo_check.status.success())
    }
    
    /// Create a helper script for privilege elevation
    pub async fn create_privilege_helper() -> Result<String> {
        let helper_script = r#"#!/bin/bash
# TUN Proxy Privilege Helper Script

echo "TUN Proxy - Network Configuration Helper"
echo "This script will configure network permissions for TUN Proxy"
echo ""

# Check if running as root
if [ "$EUID" -ne 0 ]; then
    echo "This script requires administrator privileges."
    echo "Please enter your password when prompted."
    exec sudo "$0" "$@"
fi

echo "Configuring network permissions..."

# Test TUN device creation capability
echo "Testing TUN device access..."
if command -v ifconfig >/dev/null 2>&1; then
    # Try to create a temporary TUN interface to test permissions
    if ifconfig utun99 create >/dev/null 2>&1; then
        echo "✓ TUN device creation successful"
        ifconfig utun99 destroy >/dev/null 2>&1
    else
        echo "⚠ TUN device creation requires additional permissions"
        echo "  This is normal for sandboxed applications"
    fi
else
    echo "⚠ ifconfig command not available"
fi

# Test routing table access
echo "Testing routing table access..."
if route -n get default >/dev/null 2>&1; then
    echo "✓ Routing table access successful"
else
    echo "⚠ Routing table access limited"
fi

# Set up environment for TUN Proxy
echo "Setting up TUN Proxy environment..."

# Create a flag file to indicate permissions were requested
PERM_FLAG="/tmp/.tun_proxy_permissions_requested"
echo "$(date): Permissions requested by TUN Proxy" > "$PERM_FLAG"
chmod 644 "$PERM_FLAG"

echo "Configuration complete!"
echo ""
echo "Note: TUN Proxy will need to run with elevated privileges to function properly."
echo "You can run it with: sudo npm run tauri dev"
"#;
        
        let script_path = "/tmp/tun_proxy_helper.sh";
        tokio::fs::write(script_path, helper_script).await
            .map_err(|e| TunProxyError::TunError(format!("Failed to create helper script: {}", e)))?;
        
        // Make script executable
        let output = tokio::process::Command::new("chmod")
            .arg("+x")
            .arg(script_path)
            .output()
            .await
            .map_err(|e| TunProxyError::TunError(format!("Failed to make script executable: {}", e)))?;
        
        if !output.status.success() {
            return Err(TunProxyError::TunError("Failed to make helper script executable".to_string()));
        }
        
        Ok(script_path.to_string())
    }
    
    /// Run the privilege helper script
    pub async fn run_privilege_helper() -> Result<()> {
        let script_path = Self::create_privilege_helper().await?;
        
        info!("Running privilege helper script: {}", script_path);
        
        // Use osascript to run the script with admin privileges
        let script = format!(r#"
        do shell script "{}" with administrator privileges
        "#, script_path);
        
        let output = tokio::process::Command::new("osascript")
            .arg("-e")
            .arg(&script)
            .output()
            .await
            .map_err(|e| TunProxyError::RoutingError(format!("Failed to run helper script: {}", e)))?;
        
        if output.status.success() {
            info!("Privilege helper script completed successfully");
            
            // Clean up the temporary script
            let _ = tokio::fs::remove_file(&script_path).await;
            
            Ok(())
        } else {
            let stderr = String::from_utf8_lossy(&output.stderr);
            error!("Privilege helper script failed: {}", stderr);
            
            // Clean up the temporary script
            let _ = tokio::fs::remove_file(&script_path).await;
            
            Err(TunProxyError::RoutingError(format!("Privilege helper failed: {}", stderr)))
        }
    }
    
    /// Show a native macOS dialog for permission explanation
    pub async fn show_permission_dialog(message: &str, title: &str) -> Result<bool> {
        let script = format!(r#"
        display dialog "{}" with title "{}" buttons {{"Cancel", "Open Settings", "Grant Permissions"}} default button "Grant Permissions" with icon caution
        "#, message, title);
        
        let output = tokio::process::Command::new("osascript")
            .arg("-e")
            .arg(&script)
            .output()
            .await
            .map_err(|e| TunProxyError::RoutingError(format!("Failed to show dialog: {}", e)))?;
        
        if output.status.success() {
            let result = String::from_utf8_lossy(&output.stdout);
            
            if result.contains("Grant Permissions") {
                info!("User chose to grant permissions");
                Ok(true)
            } else if result.contains("Open Settings") {
                info!("User chose to open settings");
                Self::request_security_privacy_access().await?;
                Ok(false)
            } else {
                info!("User cancelled permission request");
                Ok(false)
            }
        } else {
            // Dialog was cancelled or failed
            Ok(false)
        }
    }
    
    /// Main permission request workflow
    pub async fn request_permissions_interactive() -> Result<bool> {
        info!("Starting interactive permission request for macOS");

        // First check current status
        if Self::check_current_privileges()? {
            info!("Already have sufficient privileges");
            return Ok(true);
        }

        // Show explanation dialog
        let message = "TUN Proxy needs administrator privileges to:\n\n• Create virtual network interfaces (TUN)\n• Modify system routing tables\n• Intercept and route network traffic\n\nThis is required for system-wide proxy functionality.\n\nNote: Due to macOS security restrictions, you may need to run the application with 'sudo' for full functionality.";
        let title = "TUN Proxy - Administrator Access Required";

        let user_wants_permissions = Self::show_permission_dialog(message, title).await?;

        if !user_wants_permissions {
            return Ok(false);
        }

        // Try to get permissions
        match Self::run_privilege_helper().await {
            Ok(()) => {
                info!("Permission helper completed successfully");

                // Check if we now have better access
                tokio::time::sleep(tokio::time::Duration::from_millis(500)).await;

                if Self::check_current_privileges().unwrap_or(false) {
                    info!("Permissions verified successfully");
                    Ok(true)
                } else {
                    info!("Permission helper completed, but elevated privileges still required");

                    // Show helpful message about running with sudo
                    let sudo_message = "Permission setup completed!\n\nFor full TUN device functionality, please run:\n\nsudo npm run tauri dev\n\nThis provides the necessary system-level network access.";
                    let _ = Self::show_permission_dialog(sudo_message, "Additional Step Required").await;

                    Ok(true) // Return true since the helper ran successfully
                }
            }
            Err(e) => {
                warn!("Failed to run permission helper: {}", e);

                // Fallback: show instructions
                let fallback_message = "Automatic permission setup failed.\n\nPlease run the following command in Terminal:\n\nsudo npm run tauri dev\n\nOr grant the app permission in System Preferences > Security & Privacy.";
                let _ = Self::show_permission_dialog(fallback_message, "Manual Setup Required").await;

                Ok(false)
            }
        }
    }
}