use std::net::{<PERSON><PERSON><PERSON><PERSON><PERSON>, SocketAddr};
use std::pin::Pin;
use std::task::{Con<PERSON>, Poll};
use tokio::io::{AsyncRead, AsyncWrite, ReadBuf};
use tokio::net::{TcpStream, UdpSocket};
use async_trait::async_trait;
use crate::core::{CoreProxy, ConnectionRequest, ProxyTcpStream, ProxyUdpHandler};
use crate::error::ProxyError;
use tracing::{debug, info};

/// Dummy proxy implementation for development and testing
pub struct DummyProxy {
    nodes: Vec<String>,
    server_ips: Vec<IpAddr>,
}

impl DummyProxy {
    pub fn new() -> Self {
        Self {
            nodes: vec![
                "US-East".to_string(),
                "US-West".to_string(),
                "EU-London".to_string(),
                "Asia-Tokyo".to_string(),
            ],
            server_ips: vec![
                "*******".parse().unwrap(),
                "*******".parse().unwrap(),
                "**************".parse().unwrap(),
            ],
        }
    }
}

#[async_trait]
impl CoreProxy for DummyProxy {
    fn configure(&mut self, config_data: &str) -> Result<(), ProxyError> {
        info!("Configuring DummyProxy with config: {}", config_data);
        // In a real implementation, this would parse the config and set up nodes
        Ok(())
    }

    fn get_node_names(&self) -> Vec<String> {
        self.nodes.clone()
    }

    fn get_all_server_ips(&self) -> Vec<IpAddr> {
        self.server_ips.clone()
    }

    async fn handle_tcp_stream(
        &self,
        request: ConnectionRequest,
        node_name: &str,
    ) -> Result<Box<dyn ProxyTcpStream>, ProxyError> {
        debug!(
            "Handling TCP stream for {} via node {}",
            request.destination, node_name
        );

        // For the dummy implementation, create a direct connection
        let stream = TcpStream::connect(request.destination)
            .await
            .map_err(|_| ProxyError::NetworkError)?;

        Ok(Box::new(DummyTcpStream::new(stream)))
    }

    async fn handle_udp_association(
        &self,
        request: ConnectionRequest,
        node_name: &str,
    ) -> Result<Box<dyn ProxyUdpHandler>, ProxyError> {
        debug!(
            "Handling UDP association for {} via node {}",
            request.destination, node_name
        );

        // For the dummy implementation, create a direct UDP socket
        let socket = UdpSocket::bind("0.0.0.0:0")
            .await
            .map_err(|_| ProxyError::NetworkError)?;

        Ok(Box::new(DummyUdpHandler::new(socket)))
    }
}

/// Wrapper for TcpStream that implements ProxyTcpStream
pub struct DummyTcpStream {
    inner: TcpStream,
}

impl DummyTcpStream {
    pub fn new(stream: TcpStream) -> Self {
        Self { inner: stream }
    }
}

impl ProxyTcpStream for DummyTcpStream {}

impl AsyncRead for DummyTcpStream {
    fn poll_read(
        mut self: Pin<&mut Self>,
        cx: &mut Context<'_>,
        buf: &mut ReadBuf<'_>,
    ) -> Poll<std::io::Result<()>> {
        Pin::new(&mut self.inner).poll_read(cx, buf)
    }
}

impl AsyncWrite for DummyTcpStream {
    fn poll_write(
        mut self: Pin<&mut Self>,
        cx: &mut Context<'_>,
        buf: &[u8],
    ) -> Poll<Result<usize, std::io::Error>> {
        Pin::new(&mut self.inner).poll_write(cx, buf)
    }

    fn poll_flush(mut self: Pin<&mut Self>, cx: &mut Context<'_>) -> Poll<Result<(), std::io::Error>> {
        Pin::new(&mut self.inner).poll_flush(cx)
    }

    fn poll_shutdown(mut self: Pin<&mut Self>, cx: &mut Context<'_>) -> Poll<Result<(), std::io::Error>> {
        Pin::new(&mut self.inner).poll_shutdown(cx)
    }
}

/// Dummy UDP handler that provides direct UDP functionality
pub struct DummyUdpHandler {
    socket: UdpSocket,
}

impl DummyUdpHandler {
    pub fn new(socket: UdpSocket) -> Self {
        Self { socket }
    }
}

#[async_trait]
impl ProxyUdpHandler for DummyUdpHandler {
    async fn send_to(&self, data: &[u8], target: SocketAddr) -> std::io::Result<()> {
        self.socket.send_to(data, target).await?;
        Ok(())
    }

    async fn recv_from(&self, buf: &mut [u8]) -> std::io::Result<(usize, SocketAddr)> {
        self.socket.recv_from(buf).await
    }
}