use serde::{Deserialize, Serialize};
use aws_lc_rs::aead::{<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, AES_128_GCM};
use crate::error::{Result, TunProxyError};
use tracing::{debug, info};

/// Hardcoded configuration URL (replace with actual URL)
const CONFIG_URL: &str = "https://example.com/config.encrypted";

/// Hardcoded AES-128-GCM key (32 hex characters = 16 bytes)
/// In production, this should be properly secured
const AES_KEY: &str = "0123456789abcdef0123456789abcdef";

/// Rule types for traffic matching
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum RuleType {
    #[serde(rename = "DOMAIN-SUFFIX")]
    DomainSuffix,
    #[serde(rename = "DOMAIN")]
    Domain,
    #[serde(rename = "DOMAIN-KEYWORD")]
    DomainKeyword,
    #[serde(rename = "IP-CIDR")]
    IpCidr,
}

/// Policy for handling matched traffic
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum Policy {
    Direct,
    Reject,
    Node(String),
}

/// Individual routing rule
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Rule {
    pub rule_type: RuleType,
    pub value: String,
    pub policy: Policy,
}

/// Complete application configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AppConfig {
    /// Node configuration data passed to CoreProxy
    pub node_config: String,
    /// List of routing rules
    pub rules: Vec<Rule>,
    /// Default policy for unmatched traffic
    pub default_policy: Policy,
}

/// Configuration manager for handling encrypted config fetching and decryption
pub struct ConfigManager {
    current_config: Option<AppConfig>,
}

impl ConfigManager {
    pub fn new() -> Self {
        Self {
            current_config: None,
        }
    }

    /// Fetch and decrypt configuration from the remote server
    pub async fn fetch_config(&mut self) -> Result<&AppConfig> {
        info!("Fetching configuration from {}", CONFIG_URL);

        // Download encrypted configuration
        let client = reqwest::Client::new();
        let response = client.get(CONFIG_URL).send().await?;
        let encrypted_data = response.bytes().await?;

        debug!("Downloaded {} bytes of encrypted config", encrypted_data.len());

        // Decrypt the configuration
        let decrypted_data = self.decrypt_config(&encrypted_data)?;

        // Parse the JSON configuration
        let config: AppConfig = serde_json::from_slice(&decrypted_data)?;

        info!("Successfully loaded configuration with {} rules", config.rules.len());
        self.current_config = Some(config);

        Ok(self.current_config.as_ref().unwrap())
    }

    /// Get the current configuration (must be fetched first)
    pub fn get_config(&self) -> Option<&AppConfig> {
        self.current_config.as_ref()
    }

    /// Decrypt configuration data using AES-128-GCM
    fn decrypt_config(&self, encrypted_data: &[u8]) -> Result<Vec<u8>> {
        // Convert hex key to bytes
        let key_bytes = hex::decode(AES_KEY)
            .map_err(|_| TunProxyError::EncryptionError("Invalid key format".to_string()))?;

        if key_bytes.len() != 16 {
            return Err(TunProxyError::EncryptionError(
                "Key must be 16 bytes for AES-128".to_string(),
            ));
        }

        // The encrypted data format: [12-byte nonce][encrypted_data][16-byte tag]
        if encrypted_data.len() < 28 {
            return Err(TunProxyError::EncryptionError(
                "Encrypted data too short".to_string(),
            ));
        }

        let nonce_bytes = &encrypted_data[0..12];
        let ciphertext_and_tag = &encrypted_data[12..];

        // Create the key and nonce
        let unbound_key = UnboundKey::new(&AES_128_GCM, &key_bytes)
            .map_err(|_| TunProxyError::EncryptionError("Failed to create key".to_string()))?;
        let key = LessSafeKey::new(unbound_key);
        let nonce = Nonce::try_assume_unique_for_key(nonce_bytes)
            .map_err(|_| TunProxyError::EncryptionError("Invalid nonce".to_string()))?;

        // Decrypt the data
        let mut ciphertext = ciphertext_and_tag.to_vec();
        let plaintext = key
            .open_in_place(nonce, Aad::empty(), &mut ciphertext)
            .map_err(|_| TunProxyError::EncryptionError("Decryption failed".to_string()))?;

        Ok(plaintext.to_vec())
    }

    /// Create a dummy configuration for testing
    pub fn create_dummy_config(&mut self) -> &AppConfig {
        let dummy_config = AppConfig {
            node_config: r#"{"servers": [{"name": "US-East", "host": "us-east.example.com", "port": 443}]}"#.to_string(),
            rules: vec![
                Rule {
                    rule_type: RuleType::DomainSuffix,
                    value: "google.com".to_string(),
                    policy: Policy::Node("US-East".to_string()),
                },
                Rule {
                    rule_type: RuleType::Domain,
                    value: "github.com".to_string(),
                    policy: Policy::Direct,
                },
                Rule {
                    rule_type: RuleType::IpCidr,
                    value: "***********/16".to_string(),
                    policy: Policy::Direct,
                },
            ],
            default_policy: Policy::Node("US-East".to_string()),
        };

        self.current_config = Some(dummy_config);
        self.current_config.as_ref().unwrap()
    }
}

impl Default for ConfigManager {
    fn default() -> Self {
        Self::new()
    }
}

