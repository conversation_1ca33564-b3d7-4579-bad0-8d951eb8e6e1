use std::net::IpAddr;
use std::sync::Arc;
use tokio::sync::{mpsc, Mutex};
// Note: TUN device management is platform-specific and complex
// This is a simplified placeholder implementation
use crate::core::CoreProxy;
use crate::network::{PacketParser, NetworkStats};
use crate::rules::RuleEngine;
use crate::config::Policy;
use crate::error::{Result, TunProxyError};
use crate::privilege::PrivilegeManager;
use tracing::{debug, info, warn};

/// TUN device manager that handles traffic interception and routing
pub struct TunManager {
    device: Option<String>, // Placeholder: would be actual TUN device handle
    rule_engine: Arc<Mutex<RuleEngine>>,
    proxy: Arc<dyn CoreProxy>,
    stats: Arc<Mutex<NetworkStats>>,
    is_running: bool,
}

impl TunManager {
    pub fn new(proxy: Arc<dyn CoreProxy>) -> Self {
        Self {
            device: None,
            rule_engine: Arc::new(Mutex::new(RuleEngine::new())),
            proxy,
            stats: Arc::new(Mutex::new(NetworkStats::new())),
            is_running: false,
        }
    }

    /// Start the TUN interface and begin traffic interception
    pub async fn start(&mut self) -> Result<()> {
        if self.is_running {
            return Err(TunProxyError::TunError("TUN manager already running".to_string()));
        }

        info!("Starting TUN manager");

        // Check privileges before attempting to start
        if let Err(e) = PrivilegeManager::check_privileges() {
            return Err(TunProxyError::TunError(format!(
                "Insufficient privileges to start TUN manager.\n\n{}\n\nError: {}", 
                PrivilegeManager::get_privilege_instructions(),
                e
            )));
        }

        // Check TUN device permissions
        if let Err(e) = PrivilegeManager::check_tun_permissions() {
            return Err(TunProxyError::TunError(format!(
                "Cannot access TUN device.\n\n{}\n\nError: {}", 
                PrivilegeManager::get_privilege_instructions(),
                e
            )));
        }

        // Attempt to create a TUN device
        match self.create_tun_device().await {
            Ok(device_name) => {
                info!("TUN device created successfully: {}", device_name);
                self.device = Some(device_name);
            }
            Err(e) => {
                warn!("Failed to create TUN device: {}", e);
                info!("Running in simulation mode - no actual TUN device created");
                self.device = Some("tun0-sim".to_string()); // Simulation mode
            }
        }

        // Set up routing
        self.setup_routing().await?;

        self.device = Some("tun0".to_string()); // Placeholder device name
        self.is_running = true;

        // Start the packet processing loop
        self.start_packet_loop().await?;

        Ok(())
    }

    /// Stop the TUN interface and restore routing
    pub async fn stop(&mut self) -> Result<()> {
        if !self.is_running {
            return Ok(());
        }

        info!("Stopping TUN manager");

        self.is_running = false;

        // Restore routing
        self.restore_routing().await?;

        // Close TUN device
        self.device = None;

        info!("TUN manager stopped");
        Ok(())
    }

    /// Set up system routing to direct traffic through TUN
    async fn setup_routing(&self) -> Result<()> {
        info!("Setting up routing table");

        // Get proxy server IPs for exemption
        let server_ips = self.proxy.get_all_server_ips();

        // Get the TUN device name
        let default_device = "utun0".to_string();
        let tun_device = self.device.as_ref().unwrap_or(&default_device);

        // Build all routing commands in a single script to minimize password prompts
        let mut commands = Vec::new();

        // Add routes for proxy servers to bypass TUN
        for ip in &server_ips {
            #[cfg(target_os = "macos")]
            commands.push(format!("route add -host {} -interface en0", ip));

            #[cfg(target_os = "linux")]
            commands.push(format!("ip route add {} via $(ip route | grep default | awk '{{print $3}}' | head -1)", ip));

            #[cfg(target_os = "windows")]
            commands.push(format!("route add {} mask *************** 0.0.0.0 metric 1", ip));
        }

        // Add default route to TUN device
        #[cfg(target_os = "macos")]
        {
            commands.push(format!("route add -net 0.0.0.0/1 -interface {}", tun_device));
            commands.push(format!("route add -net *********/1 -interface {}", tun_device));
        }

        #[cfg(target_os = "linux")]
        {
            commands.push(format!("ip route add 0.0.0.0/1 dev {}", tun_device));
            commands.push(format!("ip route add *********/1 dev {}", tun_device));
        }

        #[cfg(target_os = "windows")]
        {
            commands.push(format!("route add 0.0.0.0 mask ********* {} metric 1", tun_device));
            commands.push(format!("route add ********* mask ********* {} metric 1", tun_device));
        }

        // Execute all commands in a single batch to minimize password prompts
        if !commands.is_empty() {
            info!("Executing {} routing commands in batch to minimize password prompts", commands.len());
            debug!("Routing commands: {:?}", commands);

            match PrivilegeManager::execute_privileged_commands_batch(&commands).await {
                Ok(()) => {
                    info!("Routing table configured successfully");
                }
                Err(e) => {
                    warn!("Batch routing failed, falling back to individual commands: {}", e);
                    // Fallback to individual commands if batch fails
                    for ip in &server_ips {
                        if let Err(e) = self.add_server_route(*ip).await {
                            warn!("Failed to add route for {}: {}", ip, e);
                        }
                    }

                    if let Err(e) = self.set_default_route_to_tun().await {
                        warn!("Failed to set default route: {}", e);
                    }
                }
            }
        }

        info!("Routing table configuration completed");
        Ok(())
    }

    /// Restore original routing table
    async fn restore_routing(&self) -> Result<()> {
        info!("Restoring original routing table");

        // Get proxy server IPs for cleanup
        let server_ips = self.proxy.get_all_server_ips();

        // Get the TUN device name for cleanup commands
        let default_device = "utun0".to_string();
        let _tun_device = self.device.as_ref().unwrap_or(&default_device);

        // Build all cleanup commands in a single script to minimize password prompts
        let mut commands = Vec::new();

        // Remove server-specific routes
        for ip in &server_ips {
            #[cfg(target_os = "macos")]
            commands.push(format!("route delete -host {}", ip));

            #[cfg(target_os = "linux")]
            commands.push(format!("ip route del {}", ip));

            #[cfg(target_os = "windows")]
            commands.push(format!("route delete {}", ip));
        }

        // Remove default route to TUN device
        #[cfg(target_os = "macos")]
        {
            commands.push(format!("route delete -net 0.0.0.0/1"));
            commands.push(format!("route delete -net *********/1"));
        }

        #[cfg(target_os = "linux")]
        {
            commands.push(format!("ip route del 0.0.0.0/1 dev {}", tun_device));
            commands.push(format!("ip route del *********/1 dev {}", tun_device));
        }

        #[cfg(target_os = "windows")]
        {
            commands.push(format!("route delete 0.0.0.0 mask *********"));
            commands.push(format!("route delete ********* mask *********"));
        }

        // Execute all cleanup commands in a single batch to minimize password prompts
        if !commands.is_empty() {
            info!("Executing {} cleanup commands in batch to minimize password prompts", commands.len());
            debug!("Cleanup commands: {:?}", commands);

            match PrivilegeManager::execute_privileged_commands_batch(&commands).await {
                Ok(()) => {
                    info!("Routing table restored successfully");
                }
                Err(e) => {
                    warn!("Batch cleanup failed, falling back to individual commands: {}", e);
                    // Fallback to individual commands if batch fails
                    if let Err(e) = self.remove_default_route_from_tun().await {
                        warn!("Failed to remove default route: {}", e);
                    }

                    for ip in &server_ips {
                        if let Err(e) = self.remove_server_route(*ip).await {
                            warn!("Failed to remove route for {}: {}", ip, e);
                        }
                    }
                }
            }
        }

        info!("Routing table restoration completed");
        Ok(())
    }

    /// Add a route for a proxy server to bypass TUN
    async fn add_server_route(&self, ip: IpAddr) -> Result<()> {
        debug!("Adding route for proxy server: {}", ip);

        #[cfg(target_os = "windows")]
        {
            let cmd = format!("route add {} mask *************** 0.0.0.0 metric 1", ip);
            self.execute_command(&cmd).await?;
        }

        #[cfg(target_os = "macos")]
        {
            let cmd = format!("route add -host {} -interface en0", ip);
            self.execute_command(&cmd).await?;
        }

        #[cfg(target_os = "linux")]
        {
            let cmd = format!("ip route add {} via $(ip route | grep default | awk '{{print $3}}' | head -1)", ip);
            self.execute_command(&cmd).await?;
        }

        Ok(())
    }

    /// Remove a route for a proxy server
    async fn remove_server_route(&self, ip: IpAddr) -> Result<()> {
        debug!("Removing route for proxy server: {}", ip);

        #[cfg(target_os = "windows")]
        {
            let cmd = format!("route delete {}", ip);
            self.execute_command(&cmd).await?;
        }

        #[cfg(target_os = "macos")]
        {
            let cmd = format!("route delete -host {}", ip);
            self.execute_command(&cmd).await?;
        }

        #[cfg(target_os = "linux")]
        {
            let cmd = format!("ip route del {}", ip);
            self.execute_command(&cmd).await?;
        }

        Ok(())
    }

    /// Set default route to TUN device
    async fn set_default_route_to_tun(&self) -> Result<()> {
        debug!("Setting default route to TUN device");

        #[cfg(target_os = "windows")]
        {
            // Windows TUN routing setup
            let cmd = "route add 0.0.0.0 mask 0.0.0.0 ******** metric 1";
            self.execute_command(cmd).await?;
        }

        #[cfg(target_os = "macos")]
        {
            // macOS TUN routing setup
            let cmd = "route add -net 0.0.0.0/1 -interface utun0";
            self.execute_command(cmd).await?;
            let cmd = "route add -net *********/1 -interface utun0";
            self.execute_command(cmd).await?;
        }

        #[cfg(target_os = "linux")]
        {
            // Linux TUN routing setup
            let cmd = "ip route add 0.0.0.0/1 dev tun0";
            self.execute_command(cmd).await?;
            let cmd = "ip route add *********/1 dev tun0";
            self.execute_command(cmd).await?;
        }

        Ok(())
    }

    /// Remove default route from TUN device
    async fn remove_default_route_from_tun(&self) -> Result<()> {
        debug!("Removing default route from TUN device");

        #[cfg(target_os = "windows")]
        {
            let cmd = "route delete 0.0.0.0 mask 0.0.0.0 ********";
            self.execute_command(cmd).await?;
        }

        #[cfg(target_os = "macos")]
        {
            let cmd = "route delete -net 0.0.0.0/1";
            self.execute_command(cmd).await?;
            let cmd = "route delete -net *********/1";
            self.execute_command(cmd).await?;
        }

        #[cfg(target_os = "linux")]
        {
            let cmd = "ip route del 0.0.0.0/1 dev tun0";
            self.execute_command(cmd).await?;
            let cmd = "ip route del *********/1 dev tun0";
            self.execute_command(cmd).await?;
        }

        Ok(())
    }

    /// Execute a system command with proper privilege handling
    async fn execute_command(&self, cmd: &str) -> Result<()> {
        PrivilegeManager::execute_privileged_command(cmd).await
    }

    /// Start the main packet processing loop
    async fn start_packet_loop(&mut self) -> Result<()> {
        let _device = self.device.as_ref()
            .ok_or_else(|| TunProxyError::TunError("TUN device not initialized".to_string()))?;

        let (_tx, mut rx) = mpsc::channel::<Vec<u8>>(1000);
        let rule_engine = Arc::clone(&self.rule_engine);
        let proxy = Arc::clone(&self.proxy);
        let stats = Arc::clone(&self.stats);

        // Note: This is a simplified packet loop
        // In a real implementation, you would need to handle the TUN device I/O properly
        // The exact API depends on the tun crate version and platform
        tokio::spawn(async move {
            // Placeholder for packet reading loop
            // This would read packets from the TUN device and send them for processing
            info!("Packet processing loop started (placeholder)");
        });

        // Spawn packet processor task
        tokio::spawn(async move {
            while let Some(packet_data) = rx.recv().await {
                if let Err(e) = Self::process_packet(
                    &packet_data,
                    Arc::clone(&rule_engine),
                    Arc::clone(&proxy),
                    Arc::clone(&stats),
                ).await {
                    warn!("Failed to process packet: {}", e);
                }
            }
        });

        Ok(())
    }

    /// Process a single packet
    async fn process_packet(
        packet_data: &[u8],
        rule_engine: Arc<Mutex<RuleEngine>>,
        proxy: Arc<dyn CoreProxy>,
        stats: Arc<Mutex<NetworkStats>>,
    ) -> Result<()> {
        // Parse the packet
        let connection_request = match PacketParser::parse_packet(packet_data)? {
            Some(request) => request,
            None => return Ok(()), // Skip non-TCP/UDP packets
        };

        // Evaluate the request against rules
        let policy = {
            let engine = rule_engine.lock().await;
            engine.evaluate_request(&connection_request)
        };

        // Handle the connection based on policy
        match policy {
            Policy::Direct => {
                debug!("Direct connection for {}", connection_request.destination);
                // In a real implementation, this would establish a direct connection
                // and relay traffic between TUN and the destination
            }
            Policy::Reject => {
                debug!("Rejecting connection to {}", connection_request.destination);
                // Drop the packet
            }
            Policy::Node(node_name) => {
                debug!("Proxying {} through {}", connection_request.destination, node_name);
                
                // Handle TCP or UDP through the proxy
                // This is a simplified version - real implementation would need
                // to maintain connection state and relay traffic
                match connection_request.destination.port() {
                    // Assume TCP for common ports, UDP for DNS
                    53 => {
                        if let Ok(_handler) = proxy.handle_udp_association(connection_request, &node_name).await {
                            // Set up UDP relay
                        }
                    }
                    _ => {
                        if let Ok(_stream) = proxy.handle_tcp_stream(connection_request, &node_name).await {
                            // Set up TCP relay
                        }
                    }
                }
            }
        }

        // Update statistics
        {
            let mut stats = stats.lock().await;
            stats.add_received(packet_data.len() as u64);
        }

        Ok(())
    }

    /// Get current network statistics
    pub async fn get_stats(&self) -> NetworkStats {
        let stats = self.stats.lock().await;
        stats.clone()
    }

    /// Get rule engine for configuration
    pub fn get_rule_engine(&self) -> Arc<Mutex<RuleEngine>> {
        Arc::clone(&self.rule_engine)
    }

    /// Check if TUN manager is running
    pub fn is_running(&self) -> bool {
        self.is_running
    }

    /// Create a TUN device (platform-specific implementation)
    async fn create_tun_device(&self) -> Result<String> {
        #[cfg(target_os = "macos")]
        {
            self.create_macos_tun_device().await
        }

        #[cfg(target_os = "linux")]
        {
            self.create_linux_tun_device().await
        }

        #[cfg(target_os = "windows")]
        {
            self.create_windows_tun_device().await
        }

        #[cfg(not(any(target_os = "macos", target_os = "linux", target_os = "windows")))]
        {
            Err(TunProxyError::TunError("Unsupported platform for TUN device creation".to_string()))
        }
    }

    /// Find an available utun interface on macOS
    #[cfg(target_os = "macos")]
    async fn find_available_utun_interface(&self) -> Result<String> {
        // Check existing utun interfaces
        let output = tokio::process::Command::new("ifconfig")
            .arg("-l")
            .output()
            .await
            .map_err(|e| TunProxyError::TunError(format!("Failed to list interfaces: {}", e)))?;

        if output.status.success() {
            let interfaces = String::from_utf8_lossy(&output.stdout);
            debug!("Available interfaces: {}", interfaces);

            // Look for existing utun interfaces
            for interface in interfaces.split_whitespace() {
                if interface.starts_with("utun") {
                    // Check if this interface is already configured
                    let check_output = tokio::process::Command::new("ifconfig")
                        .arg(interface)
                        .output()
                        .await;

                    if let Ok(check_result) = check_output {
                        let config = String::from_utf8_lossy(&check_result.stdout);
                        if !config.contains("********") {
                            // This interface is available for our use
                            info!("Found available utun interface: {}", interface);
                            return Ok(interface.to_string());
                        }
                    }
                }
            }
        }

        // If no existing utun interface is available, use utun0 as default
        // (it will be created automatically when we try to configure it)
        Ok("utun0".to_string())
    }

    /// Create a TUN device on macOS
    #[cfg(target_os = "macos")]
    async fn create_macos_tun_device(&self) -> Result<String> {
        info!("Attempting to create macOS TUN device");

        // First check if we have the necessary permissions
        if let Err(_) = PrivilegeManager::check_privileges() {
            return Err(TunProxyError::TunError(
                "Insufficient privileges to create TUN device. Please run with administrator privileges or use 'Grant Permissions'.".to_string()
            ));
        }

        // On macOS, utun interfaces are created automatically when opened
        // Let's try to find an available utun interface or use an existing one
        let available_utun = self.find_available_utun_interface().await?;

        // Configure the interface
        let config_cmd = format!("ifconfig {} ******** 10.0.0.2 up", available_utun);
        match PrivilegeManager::execute_privileged_command(&config_cmd).await {
            Ok(()) => {
                info!("Successfully configured {} interface", available_utun);
                Ok(available_utun)
            }
            Err(e) => {
                warn!("Failed to configure {}: {}", available_utun, e);

                // Provide helpful error message based on the error type
                let error_msg = if e.to_string().contains("password") || e.to_string().contains("sudo") {
                    "TUN device configuration requires administrator privileges. Please:\n\
                    1. Run the application with: sudo npm run tauri dev\n\
                    2. Or use the 'Grant Permissions' button and enter your password when prompted\n\
                    3. For production builds: sudo ./tun-proxy".to_string()
                } else {
                    format!("TUN device configuration failed: {}. This may require running the application with 'sudo'.", e)
                };

                Err(TunProxyError::TunError(error_msg))
            }
        }
    }

    /// Create a TUN device on Linux
    #[cfg(target_os = "linux")]
    async fn create_linux_tun_device(&self) -> Result<String> {
        info!("Attempting to create Linux TUN device");

        // Use ip command to create TUN device
        let create_cmd = "ip tuntap add dev tun0 mode tun";
        match PrivilegeManager::execute_privileged_command(create_cmd).await {
            Ok(()) => {
                let config_cmd = "ip addr add ********/24 dev tun0 && ip link set tun0 up";
                match PrivilegeManager::execute_privileged_command(config_cmd).await {
                    Ok(()) => {
                        info!("Successfully created and configured tun0");
                        Ok("tun0".to_string())
                    }
                    Err(e) => {
                        let _ = PrivilegeManager::execute_privileged_command("ip tuntap del dev tun0 mode tun").await;
                        Err(TunProxyError::TunError(format!("Failed to configure TUN device: {}", e)))
                    }
                }
            }
            Err(e) => {
                Err(TunProxyError::TunError(format!("TUN device creation failed: {}", e)))
            }
        }
    }

    /// Create a TUN device on Windows
    #[cfg(target_os = "windows")]
    async fn create_windows_tun_device(&self) -> Result<String> {
        info!("Attempting to create Windows TUN device");

        // Windows TUN device creation is more complex and typically requires
        // a TAP-Windows adapter or WinTun driver
        Err(TunProxyError::TunError(
            "Windows TUN device creation not yet implemented. Please install TAP-Windows adapter.".to_string()
        ))
    }
}