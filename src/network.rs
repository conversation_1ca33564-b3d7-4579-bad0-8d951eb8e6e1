use std::net::{<PERSON><PERSON><PERSON><PERSON><PERSON>, SocketAddr};
use smoltcp::wire::{IpProtocol, Ipv4Packet, Ipv6Packet, TcpPacket, UdpPacket};
use crate::core::ConnectionRequest;
use crate::error::{Result, TunProxyError};
use tracing::{debug, trace, warn};

/// Network packet parser for extracting connection information from raw IP packets
pub struct PacketParser;

impl PacketParser {
    /// Parse a raw IP packet and extract connection request information
    pub fn parse_packet(data: &[u8]) -> Result<Option<ConnectionRequest>> {
        if data.is_empty() {
            return Ok(None);
        }

        // Determine IP version from the first nibble
        let version = (data[0] >> 4) & 0x0F;

        match version {
            4 => Self::parse_ipv4_packet(data),
            6 => Self::parse_ipv6_packet(data),
            _ => {
                warn!("Unknown IP version: {}", version);
                Ok(None)
            }
        }
    }

    /// Parse an IPv4 packet
    fn parse_ipv4_packet(data: &[u8]) -> Result<Option<ConnectionRequest>> {
        let ipv4_packet = Ipv4Packet::new_checked(data)
            .map_err(|e| TunProxyError::NetworkError(
                std::io::Error::new(std::io::ErrorKind::InvalidData, format!("Invalid IPv4 packet: {:?}", e))
            ))?;

        let src_ip = IpAddr::V4(ipv4_packet.src_addr().into());
        let dst_ip = IpAddr::V4(ipv4_packet.dst_addr().into());
        let protocol = ipv4_packet.next_header();

        trace!("IPv4 packet: {} -> {}, protocol: {:?}", src_ip, dst_ip, protocol);

        Self::parse_transport_layer(&ipv4_packet.payload(), protocol, src_ip, dst_ip)
    }

    /// Parse an IPv6 packet
    fn parse_ipv6_packet(data: &[u8]) -> Result<Option<ConnectionRequest>> {
        let ipv6_packet = Ipv6Packet::new_checked(data)
            .map_err(|e| TunProxyError::NetworkError(
                std::io::Error::new(std::io::ErrorKind::InvalidData, format!("Invalid IPv6 packet: {:?}", e))
            ))?;

        let src_ip = IpAddr::V6(ipv6_packet.src_addr().into());
        let dst_ip = IpAddr::V6(ipv6_packet.dst_addr().into());
        let protocol = ipv6_packet.next_header();

        trace!("IPv6 packet: {} -> {}, protocol: {:?}", src_ip, dst_ip, protocol);

        Self::parse_transport_layer(&ipv6_packet.payload(), protocol, src_ip, dst_ip)
    }

    /// Parse the transport layer (TCP/UDP) to extract port information
    fn parse_transport_layer(
        payload: &[u8],
        protocol: IpProtocol,
        src_ip: IpAddr,
        dst_ip: IpAddr,
    ) -> Result<Option<ConnectionRequest>> {
        match protocol {
            IpProtocol::Tcp => {
                let tcp_packet = TcpPacket::new_checked(payload)
                    .map_err(|e| TunProxyError::NetworkError(
                        std::io::Error::new(std::io::ErrorKind::InvalidData, format!("Invalid TCP packet: {:?}", e))
                    ))?;

                let src_port = tcp_packet.src_port();
                let dst_port = tcp_packet.dst_port();

                let source = SocketAddr::new(src_ip, src_port);
                let destination = SocketAddr::new(dst_ip, dst_port);

                debug!("TCP connection: {} -> {}", source, destination);

                Ok(Some(ConnectionRequest { source, destination }))
            }
            IpProtocol::Udp => {
                let udp_packet = UdpPacket::new_checked(payload)
                    .map_err(|e| TunProxyError::NetworkError(
                        std::io::Error::new(std::io::ErrorKind::InvalidData, format!("Invalid UDP packet: {:?}", e))
                    ))?;

                let src_port = udp_packet.src_port();
                let dst_port = udp_packet.dst_port();

                let source = SocketAddr::new(src_ip, src_port);
                let destination = SocketAddr::new(dst_ip, dst_port);

                debug!("UDP association: {} -> {}", source, destination);

                Ok(Some(ConnectionRequest { source, destination }))
            }
            _ => {
                trace!("Ignoring non-TCP/UDP packet: {:?}", protocol);
                Ok(None)
            }
        }
    }
}

/// Network statistics tracker
#[derive(Debug, Clone, Default)]
pub struct NetworkStats {
    pub bytes_sent: u64,
    pub bytes_received: u64,
    pub packets_sent: u64,
    pub packets_received: u64,
    pub connections_active: u32,
    pub connections_total: u64,
}

impl NetworkStats {
    pub fn new() -> Self {
        Self::default()
    }

    pub fn add_sent(&mut self, bytes: u64) {
        self.bytes_sent += bytes;
        self.packets_sent += 1;
    }

    pub fn add_received(&mut self, bytes: u64) {
        self.bytes_received += bytes;
        self.packets_received += 1;
    }

    pub fn connection_opened(&mut self) {
        self.connections_active += 1;
        self.connections_total += 1;
    }

    pub fn connection_closed(&mut self) {
        if self.connections_active > 0 {
            self.connections_active -= 1;
        }
    }

    pub fn get_upload_speed(&self, duration_secs: f64) -> f64 {
        if duration_secs > 0.0 {
            self.bytes_sent as f64 / duration_secs
        } else {
            0.0
        }
    }

    pub fn get_download_speed(&self, duration_secs: f64) -> f64 {
        if duration_secs > 0.0 {
            self.bytes_received as f64 / duration_secs
        } else {
            0.0
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_packet_parser_empty() {
        let result = PacketParser::parse_packet(&[]);
        assert!(result.is_ok());
        assert!(result.unwrap().is_none());
    }

    #[test]
    fn test_network_stats() {
        let mut stats = NetworkStats::new();
        
        stats.add_sent(1024);
        stats.add_received(2048);
        stats.connection_opened();
        
        assert_eq!(stats.bytes_sent, 1024);
        assert_eq!(stats.bytes_received, 2048);
        assert_eq!(stats.connections_active, 1);
        assert_eq!(stats.connections_total, 1);
        
        stats.connection_closed();
        assert_eq!(stats.connections_active, 0);
    }
}