import React, { useState } from 'react';
import { invoke } from '@tauri-apps/api/tauri';
import { Shield, Zap, Settings, CheckCircle, AlertTriangle, X } from 'lucide-react';

interface MacOSPermissionDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onPermissionGranted: () => void;
}

const MacOSPermissionDialog: React.FC<MacOSPermissionDialogProps> = ({
  isOpen,
  onClose,
  onPermissionGranted,
}) => {
  const [step, setStep] = useState<'intro' | 'requesting' | 'success' | 'failed'>('intro');
  const [errorMessage, setErrorMessage] = useState('');

  if (!isOpen) return null;

  const handleRequestPermissions = async () => {
    setStep('requesting');
    
    try {
      const granted = await invoke<boolean>('request_permissions');
      
      if (granted) {
        setStep('success');
        // 立即通知权限已授予
        onPermissionGranted();
        // 延迟关闭对话框，让用户看到成功状态
        setTimeout(() => {
          onClose();
        }, 2000);
      } else {
        setStep('failed');
        setErrorMessage('Permission request was cancelled or denied');
      }
    } catch (error) {
      setStep('failed');
      setErrorMessage(typeof error === 'string' ? error : 'Failed to request permissions');
    }
  };

  const renderContent = () => {
    switch (step) {
      case 'intro':
        return (
          <>
            <div className="flex items-center space-x-3 mb-6">
              <Shield className="w-8 h-8 text-blue-400" />
              <div>
                <h2 className="text-xl font-semibold text-white">Administrator Access Required</h2>
                <p className="text-gray-400">TUN Proxy needs elevated privileges to function</p>
              </div>
            </div>

            <div className="space-y-4 mb-6">
              <div className="bg-gray-800 rounded-lg p-4">
                <h3 className="font-medium text-gray-200 mb-3">Why are these permissions needed?</h3>
                <ul className="space-y-2 text-sm text-gray-300">
                  <li className="flex items-center space-x-2">
                    <div className="w-2 h-2 bg-blue-400 rounded-full"></div>
                    <span>Create virtual network interfaces (TUN devices)</span>
                  </li>
                  <li className="flex items-center space-x-2">
                    <div className="w-2 h-2 bg-blue-400 rounded-full"></div>
                    <span>Modify system routing tables</span>
                  </li>
                  <li className="flex items-center space-x-2">
                    <div className="w-2 h-2 bg-blue-400 rounded-full"></div>
                    <span>Intercept and route network traffic</span>
                  </li>
                </ul>
              </div>

              <div className="bg-yellow-500/10 border border-yellow-500/20 rounded-lg p-4">
                <div className="flex items-start space-x-3">
                  <AlertTriangle className="w-5 h-5 text-yellow-400 mt-0.5 flex-shrink-0" />
                  <div>
                    <h4 className="font-medium text-yellow-400 mb-1">What will happen:</h4>
                    <p className="text-sm text-yellow-300">
                      macOS will show a system dialog asking for your administrator password. 
                      This is required for system-wide network proxy functionality.
                    </p>
                  </div>
                </div>
              </div>
            </div>

            <div className="flex space-x-3">
              <button
                onClick={handleRequestPermissions}
                className="btn-primary flex items-center space-x-2 flex-1"
              >
                <Zap className="w-4 h-4" />
                <span>Grant Permissions</span>
              </button>
              <button
                onClick={onClose}
                className="btn-secondary"
              >
                Cancel
              </button>
            </div>
          </>
        );

      case 'requesting':
        return (
          <>
            <div className="text-center mb-6">
              <div className="w-16 h-16 mx-auto mb-4 relative">
                <div className="w-16 h-16 border-4 border-blue-500 border-t-transparent rounded-full animate-spin"></div>
                <Shield className="w-8 h-8 text-blue-400 absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2" />
              </div>
              <h2 className="text-xl font-semibold text-white mb-2">Requesting Permissions</h2>
              <p className="text-gray-400">
                Please enter your administrator password in the system dialog
              </p>
            </div>

            <div className="bg-blue-500/10 border border-blue-500/20 rounded-lg p-4">
              <p className="text-sm text-blue-300">
                A macOS system dialog should appear asking for your password. 
                If you don't see it, check if it's behind other windows.
              </p>
            </div>
          </>
        );

      case 'success':
        return (
          <>
            <div className="text-center mb-6">
              <CheckCircle className="w-16 h-16 text-green-400 mx-auto mb-4" />
              <h2 className="text-xl font-semibold text-white mb-2">Permissions Granted!</h2>
              <p className="text-gray-400">
                TUN Proxy now has the necessary privileges to function
              </p>
            </div>

            <div className="bg-green-500/10 border border-green-500/20 rounded-lg p-4">
              <p className="text-sm text-green-300">
                You can now connect to the proxy. This dialog will close automatically.
              </p>
            </div>
          </>
        );

      case 'failed':
        return (
          <>
            <div className="text-center mb-6">
              <AlertTriangle className="w-16 h-16 text-red-400 mx-auto mb-4" />
              <h2 className="text-xl font-semibold text-white mb-2">Permission Request Failed</h2>
              <p className="text-gray-400">
                Unable to obtain the necessary privileges
              </p>
            </div>

            <div className="bg-red-500/10 border border-red-500/20 rounded-lg p-4 mb-6">
              <p className="text-sm text-red-300 mb-2">Error:</p>
              <p className="text-xs text-red-200 font-mono">{errorMessage}</p>
            </div>

            <div className="bg-gray-800 rounded-lg p-4 mb-6">
              <h3 className="font-medium text-gray-200 mb-2">Alternative solutions:</h3>
              <ul className="space-y-1 text-sm text-gray-300">
                <li>• Run the app from Terminal with: <code className="bg-gray-700 px-1 rounded">sudo npm run tauri dev</code></li>
                <li>• Check System Preferences → Security & Privacy</li>
                <li>• Ensure you entered the correct administrator password</li>
              </ul>
            </div>

            <div className="flex space-x-3">
              <button
                onClick={() => setStep('intro')}
                className="btn-primary flex items-center space-x-2"
              >
                <Zap className="w-4 h-4" />
                <span>Try Again</span>
              </button>
              <button
                onClick={onClose}
                className="btn-secondary"
              >
                Close
              </button>
            </div>
          </>
        );

      default:
        return null;
    }
  };

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
      <div className="bg-gray-800 rounded-xl border border-gray-700 shadow-2xl max-w-md w-full">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-700">
          <div className="flex items-center space-x-2">
            <Settings className="w-5 h-5 text-gray-400" />
            <span className="font-medium text-gray-200">macOS Permissions</span>
          </div>
          {step !== 'requesting' && (
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-white transition-colors"
            >
              <X className="w-5 h-5" />
            </button>
          )}
        </div>

        {/* Content */}
        <div className="p-6">
          {renderContent()}
        </div>
      </div>
    </div>
  );
};

export default MacOSPermissionDialog;