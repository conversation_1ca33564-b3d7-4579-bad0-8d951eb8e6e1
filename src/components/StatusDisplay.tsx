import React from 'react';
import { Globe, Shield, Zap } from 'lucide-react';
import { ConnectionInfo } from '../types';

interface StatusDisplayProps {
  connectionInfo: ConnectionInfo | null;
  proxyMode: string;
  selectedNode: string | null;
}

const StatusDisplay: React.FC<StatusDisplayProps> = ({
  connectionInfo,
  proxyMode,
  selectedNode,
}) => {
  const getModeIcon = () => {
    switch (proxyMode) {
      case 'Global':
        return <Globe className="w-5 h-5" />;
      case 'Rule':
        return <Shield className="w-5 h-5" />;
      case 'Direct':
        return <Zap className="w-5 h-5" />;
      default:
        return <Shield className="w-5 h-5" />;
    }
  };

  const getModeDescription = () => {
    switch (proxyMode) {
      case 'Global':
        return 'All traffic routed through proxy';
      case 'Rule':
        return 'Traffic routed based on rules';
      case 'Direct':
        return 'Direct connection, no proxy';
      default:
        return 'Unknown mode';
    }
  };

  const getConnectionStatusColor = () => {
    if (!connectionInfo) return 'text-gray-400';
    
    if (connectionInfo.status === 'Disconnected') return 'text-gray-400';
    if (connectionInfo.status === 'Connecting') return 'text-primary-400';
    if (typeof connectionInfo.status === 'object' && 'Connected' in connectionInfo.status) return 'text-green-400';
    if (typeof connectionInfo.status === 'object' && 'Error' in connectionInfo.status) return 'text-red-400';
    
    return 'text-gray-400';
  };

  const getConnectionStatusText = () => {
    if (!connectionInfo) return 'Unknown';
    
    if (connectionInfo.status === 'Disconnected') return 'Disconnected';
    if (connectionInfo.status === 'Connecting') return 'Connecting';
    if (typeof connectionInfo.status === 'object' && 'Connected' in connectionInfo.status) return 'Connected';
    if (typeof connectionInfo.status === 'object' && 'Error' in connectionInfo.status) return 'Error';
    
    return 'Unknown';
  };

  return (
    <div className="text-center space-y-4">
      {/* Connection Status */}
      <div className="flex items-center justify-center space-x-2">
        <div className={`w-3 h-3 rounded-full ${getConnectionStatusColor().replace('text-', 'bg-')}`}></div>
        <span className={`text-lg font-medium ${getConnectionStatusColor()}`}>
          {getConnectionStatusText()}
        </span>
      </div>

      {/* Mode and Node Info */}
      <div className="space-y-2">
        <div className="flex items-center justify-center space-x-2 text-gray-300">
          {getModeIcon()}
          <span className="font-medium">{proxyMode} Mode</span>
        </div>
        
        <p className="text-sm text-gray-400">
          {getModeDescription()}
        </p>

        {selectedNode && proxyMode !== 'Direct' && (
          <div className="mt-2">
            <span className="text-sm text-gray-400">Active Node: </span>
            <span className="text-sm font-medium text-primary-400">{selectedNode}</span>
          </div>
        )}
      </div>
    </div>
  );
};

export default StatusDisplay;