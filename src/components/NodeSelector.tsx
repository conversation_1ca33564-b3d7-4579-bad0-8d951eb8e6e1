import React from 'react';
import { Server, ChevronDown } from 'lucide-react';

interface NodeSelectorProps {
  selectedNode: string | null;
  availableNodes: string[];
  disabled?: boolean;
  onNodeChange: (node: string | null) => void;
}

const NodeSelector: React.FC<NodeSelectorProps> = ({
  selectedNode,
  availableNodes,
  disabled = false,
  onNodeChange,
}) => {
  return (
    <div className="space-y-3">
      <div className="flex items-center space-x-2 text-gray-400">
        <Server className="w-4 h-4" />
        <span className="text-sm">
          {disabled ? 'Node selection disabled in Direct mode' : 'Select proxy node'}
        </span>
      </div>

      <div className="relative">
        <select
          value={selectedNode || ''}
          onChange={(e) => onNodeChange(e.target.value || null)}
          disabled={disabled}
          className={`w-full select pr-10 ${
            disabled 
              ? 'bg-gray-800/50 text-gray-500 cursor-not-allowed' 
              : 'bg-gray-700 text-gray-100 hover:bg-gray-600'
          }`}
        >
          <option value="">Select a node...</option>
          {availableNodes.map((node) => (
            <option key={node} value={node}>
              {node}
            </option>
          ))}
        </select>
        
        <ChevronDown className={`absolute right-3 top-1/2 transform -translate-y-1/2 w-4 h-4 pointer-events-none ${
          disabled ? 'text-gray-600' : 'text-gray-400'
        }`} />
      </div>

      {selectedNode && !disabled && (
        <div className="p-3 bg-gray-700/50 rounded-lg border border-gray-600">
          <div className="flex items-center space-x-2">
            <div className="w-2 h-2 rounded-full bg-green-500"></div>
            <span className="text-sm font-medium text-gray-200">
              Selected: {selectedNode}
            </span>
          </div>
          <p className="text-xs text-gray-400 mt-1">
            Traffic will be routed through this node
          </p>
        </div>
      )}

      {availableNodes.length === 0 && !disabled && (
        <div className="p-3 bg-yellow-500/10 border border-yellow-500/20 rounded-lg">
          <p className="text-sm text-yellow-400">
            No nodes available. Check your configuration.
          </p>
        </div>
      )}
    </div>
  );
};

export default NodeSelector;