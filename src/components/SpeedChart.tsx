import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON><PERSON>, Line, XAxis, <PERSON>Axis, CartesianGrid, Tooltip, ResponsiveContainer } from 'recharts';
import { Upload, Download } from 'lucide-react';
import { NetworkStats, SpeedDataPoint } from '../types';

interface SpeedChartProps {
  networkStats: NetworkStats | null;
}

const SpeedChart: React.FC<SpeedChartProps> = ({ networkStats }) => {
  const [speedData, setSpeedData] = useState<SpeedDataPoint[]>([]);

  useEffect(() => {
    if (networkStats) {
      const now = Date.now();
      const newDataPoint: SpeedDataPoint = {
        timestamp: now,
        upload: networkStats.upload_speed,
        download: networkStats.download_speed,
      };

      setSpeedData(prev => {
        const updated = [...prev, newDataPoint];
        // Keep only last 60 data points (60 seconds)
        return updated.slice(-60);
      });
    }
  }, [networkStats]);

  const formatSpeed = (bytesPerSecond: number): string => {
    if (bytesPerSecond === 0) return '0 B/s';
    
    const units = ['B/s', 'KB/s', 'MB/s', 'GB/s'];
    const k = 1024;
    const i = Math.floor(Math.log(bytesPerSecond) / Math.log(k));
    
    return `${(bytesPerSecond / Math.pow(k, i)).toFixed(1)} ${units[i]}`;
  };

  const formatBytes = (bytes: number): string => {
    if (bytes === 0) return '0 B';
    
    const units = ['B', 'KB', 'MB', 'GB', 'TB'];
    const k = 1024;
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    
    return `${(bytes / Math.pow(k, i)).toFixed(1)} ${units[i]}`;
  };

  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      const time = new Date(label).toLocaleTimeString();
      return (
        <div className="bg-gray-800 border border-gray-600 rounded-lg p-3 shadow-lg">
          <p className="text-gray-300 text-sm mb-2">{time}</p>
          {payload.map((entry: any, index: number) => (
            <p key={index} className="text-sm" style={{ color: entry.color }}>
              {entry.name}: {formatSpeed(entry.value)}
            </p>
          ))}
        </div>
      );
    }
    return null;
  };

  return (
    <div className="space-y-4">
      {/* Current Speed Display */}
      <div className="grid grid-cols-2 gap-4">
        <div className="flex items-center space-x-3 p-3 bg-gray-700/50 rounded-lg">
          <Upload className="w-5 h-5 text-green-400" />
          <div>
            <p className="text-sm text-gray-400">Upload</p>
            <p className="font-medium text-green-400">
              {networkStats ? formatSpeed(networkStats.upload_speed) : '0 B/s'}
            </p>
          </div>
        </div>
        
        <div className="flex items-center space-x-3 p-3 bg-gray-700/50 rounded-lg">
          <Download className="w-5 h-5 text-blue-400" />
          <div>
            <p className="text-sm text-gray-400">Download</p>
            <p className="font-medium text-blue-400">
              {networkStats ? formatSpeed(networkStats.download_speed) : '0 B/s'}
            </p>
          </div>
        </div>
      </div>

      {/* Speed Chart */}
      <div className="h-64 w-full">
        <ResponsiveContainer width="100%" height="100%">
          <LineChart data={speedData}>
            <CartesianGrid strokeDasharray="3 3" stroke="#374151" />
            <XAxis 
              dataKey="timestamp"
              type="number"
              scale="time"
              domain={['dataMin', 'dataMax']}
              tickFormatter={(value) => new Date(value).toLocaleTimeString([], { 
                hour: '2-digit', 
                minute: '2-digit',
                second: '2-digit'
              })}
              stroke="#9CA3AF"
              fontSize={12}
            />
            <YAxis 
              tickFormatter={formatSpeed}
              stroke="#9CA3AF"
              fontSize={12}
            />
            <Tooltip content={<CustomTooltip />} />
            <Line
              type="monotone"
              dataKey="upload"
              stroke="#10B981"
              strokeWidth={2}
              dot={false}
              name="Upload"
            />
            <Line
              type="monotone"
              dataKey="download"
              stroke="#3B82F6"
              strokeWidth={2}
              dot={false}
              name="Download"
            />
          </LineChart>
        </ResponsiveContainer>
      </div>

      {/* Statistics */}
      {networkStats && (
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
          <div className="text-center p-2 bg-gray-700/30 rounded">
            <p className="text-gray-400">Total Sent</p>
            <p className="font-medium">{formatBytes(networkStats.bytes_sent)}</p>
          </div>
          <div className="text-center p-2 bg-gray-700/30 rounded">
            <p className="text-gray-400">Total Received</p>
            <p className="font-medium">{formatBytes(networkStats.bytes_received)}</p>
          </div>
          <div className="text-center p-2 bg-gray-700/30 rounded">
            <p className="text-gray-400">Active Connections</p>
            <p className="font-medium">{networkStats.connections_active}</p>
          </div>
          <div className="text-center p-2 bg-gray-700/30 rounded">
            <p className="text-gray-400">Total Connections</p>
            <p className="font-medium">{networkStats.connections_total}</p>
          </div>
        </div>
      )}
    </div>
  );
};

export default SpeedChart;