import React, { useEffect, useRef } from 'react';
import { X, Co<PERSON>, Trash2, AlertCircle, Info, AlertTriangle } from 'lucide-react';
import { LogEntry } from '../types';

interface LogViewerProps {
  logs: LogEntry[];
  onClose: () => void;
  onClear: () => void;
}

const LogViewer: React.FC<LogViewerProps> = ({ logs, onClose, onClear }) => {
  const scrollRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    // Auto-scroll to bottom when new logs arrive
    if (scrollRef.current) {
      scrollRef.current.scrollTop = scrollRef.current.scrollHeight;
    }
  }, [logs]);

  const getLevelIcon = (level: string) => {
    switch (level.toUpperCase()) {
      case 'ERROR':
        return <AlertCircle className="w-4 h-4 text-red-400" />;
      case 'WARN':
        return <AlertTriangle className="w-4 h-4 text-yellow-400" />;
      case 'INFO':
        return <Info className="w-4 h-4 text-blue-400" />;
      default:
        return <Info className="w-4 h-4 text-gray-400" />;
    }
  };

  const getLevelColor = (level: string) => {
    switch (level.toUpperCase()) {
      case 'ERROR':
        return 'text-red-400';
      case 'WARN':
        return 'text-yellow-400';
      case 'INFO':
        return 'text-blue-400';
      default:
        return 'text-gray-400';
    }
  };

  const formatTimestamp = (timestamp: string) => {
    try {
      const date = new Date(timestamp);
      return date.toLocaleTimeString([], {
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit',
      }) + '.' + date.getMilliseconds().toString().padStart(3, '0');
    } catch {
      return timestamp;
    }
  };

  const copyLogs = async () => {
    const logText = logs
      .map(log => `[${formatTimestamp(log.timestamp)}] ${log.level}: ${log.message}`)
      .join('\n');
    
    try {
      await navigator.clipboard.writeText(logText);
    } catch (error) {
      console.error('Failed to copy logs:', error);
    }
  };

  return (
    <div className="fixed inset-x-0 bottom-0 h-1/3 bg-gray-800 border-t border-gray-700 shadow-2xl z-50 animate-slide-up">
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b border-gray-700">
        <div className="flex items-center space-x-2">
          <h3 className="text-lg font-semibold text-white">Application Logs</h3>
          <span className="text-sm text-gray-400">({logs.length} entries)</span>
        </div>
        
        <div className="flex items-center space-x-2">
          <button
            onClick={copyLogs}
            className="p-2 text-gray-400 hover:text-white hover:bg-gray-700 rounded-lg transition-colors"
            title="Copy Logs"
          >
            <Copy className="w-4 h-4" />
          </button>
          
          <button
            onClick={onClear}
            className="p-2 text-gray-400 hover:text-red-400 hover:bg-gray-700 rounded-lg transition-colors"
            title="Clear Logs"
          >
            <Trash2 className="w-4 h-4" />
          </button>
          
          <button
            onClick={onClose}
            className="p-2 text-gray-400 hover:text-white hover:bg-gray-700 rounded-lg transition-colors"
            title="Close Logs"
          >
            <X className="w-4 h-4" />
          </button>
        </div>
      </div>

      {/* Log Content */}
      <div 
        ref={scrollRef}
        className="h-full overflow-y-auto p-4 space-y-1 font-mono text-sm"
      >
        {logs.length === 0 ? (
          <div className="flex items-center justify-center h-full text-gray-500">
            <p>No logs available</p>
          </div>
        ) : (
          logs.map((log, index) => (
            <div
              key={index}
              className="flex items-start space-x-3 p-2 hover:bg-gray-700/50 rounded group"
            >
              <span className="text-gray-500 text-xs mt-0.5 w-20 flex-shrink-0">
                {formatTimestamp(log.timestamp)}
              </span>
              
              <div className="flex items-center space-x-2 w-16 flex-shrink-0">
                {getLevelIcon(log.level)}
                <span className={`text-xs font-medium ${getLevelColor(log.level)}`}>
                  {log.level.toUpperCase()}
                </span>
              </div>
              
              <span className="text-gray-300 flex-1 break-words">
                {log.message}
              </span>
            </div>
          ))
        )}
      </div>
    </div>
  );
};

export default LogViewer;