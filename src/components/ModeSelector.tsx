import React from 'react';
import { Globe, Shield, Zap } from 'lucide-react';

interface ModeSelectorProps {
  currentMode: string;
  onModeChange: (mode: string) => void;
}

const ModeSelector: React.FC<ModeSelectorProps> = ({
  currentMode,
  onModeChange,
}) => {
  const modes = [
    {
      id: 'Global',
      name: 'Global',
      icon: Globe,
      description: 'Route all traffic through proxy',
    },
    {
      id: 'Rule',
      name: 'Rule',
      icon: Shield,
      description: 'Use rule-based routing',
    },
    {
      id: 'Direct',
      name: 'Direct',
      icon: Zap,
      description: 'Direct connection, no proxy',
    },
  ];

  return (
    <div className="space-y-3">
      {modes.map((mode) => {
        const Icon = mode.icon;
        const isSelected = currentMode === mode.id;
        
        return (
          <button
            key={mode.id}
            onClick={() => onModeChange(mode.id)}
            className={`w-full p-3 rounded-lg border transition-all duration-200 text-left ${
              isSelected
                ? 'border-primary-500 bg-primary-500/10 text-primary-400'
                : 'border-gray-600 bg-gray-700/50 text-gray-300 hover:border-gray-500 hover:bg-gray-700'
            }`}
          >
            <div className="flex items-center space-x-3">
              <Icon className={`w-5 h-5 ${isSelected ? 'text-primary-400' : 'text-gray-400'}`} />
              <div className="flex-1">
                <div className="font-medium">{mode.name}</div>
                <div className={`text-sm ${isSelected ? 'text-primary-300' : 'text-gray-500'}`}>
                  {mode.description}
                </div>
              </div>
              {isSelected && (
                <div className="w-2 h-2 rounded-full bg-primary-500"></div>
              )}
            </div>
          </button>
        );
      })}
    </div>
  );
};

export default ModeSelector;