import React, { useState, useEffect } from 'react';
import { invoke } from '@tauri-apps/api/tauri';
import { AlertTriangle, Shield, X, Settings, Zap } from 'lucide-react';
import MacOSPermissionDialog from './MacOSPermissionDialog';
import SuccessNotification from './SuccessNotification';

interface PrivilegeWarningProps {
  onClose?: () => void;
}

const PrivilegeWarning: React.FC<PrivilegeWarningProps> = ({ onClose }) => {
  const [hasPrivileges, setHasPrivileges] = useState<boolean | null>(null);
  const [instructions, setInstructions] = useState<string>('');
  const [isVisible, setIsVisible] = useState(true);
  const [isMacOS, setIsMacOS] = useState(false);
  const [showPermissionDialog, setShowPermissionDialog] = useState(false);
  const [isChecking, setIsChecking] = useState(false);
  const [showSuccessNotification, setShowSuccessNotification] = useState(false);
  const [previousPrivilegeState, setPreviousPrivilegeState] = useState<boolean | null>(null);

  useEffect(() => {
    checkPrivileges();
    detectPlatform();
  }, []);

  const detectPlatform = () => {
    // Detect if we're on macOS
    const userAgent = navigator.userAgent.toLowerCase();
    setIsMacOS(userAgent.includes('mac'));
  };

  const checkPrivileges = async () => {
    setIsChecking(true);
    try {
      const [privilegeCheck, privilegeInstructions] = await Promise.all([
        invoke<boolean>('check_privileges'),
        invoke<string>('get_privilege_instructions'),
      ]);
      
      console.log('Privilege check result:', privilegeCheck);
      
      // Check if privileges were just granted
      if (previousPrivilegeState === false && privilegeCheck === true) {
        console.log('Privileges were just granted!');
        setShowSuccessNotification(true);
      }
      
      setPreviousPrivilegeState(privilegeCheck);
      setHasPrivileges(privilegeCheck);
      setInstructions(privilegeInstructions);
    } catch (error) {
      console.error('Failed to check privileges:', error);
      setHasPrivileges(false);
    } finally {
      setIsChecking(false);
    }
  };

  const handleClose = () => {
    setIsVisible(false);
    if (onClose) {
      onClose();
    }
  };

  const requestPermissions = () => {
    if (!isMacOS) {
      return;
    }
    setShowPermissionDialog(true);
  };

  const handlePermissionGranted = async () => {
    // Immediately recheck privileges after granting
    console.log('Permission granted, rechecking privileges...');
    
    // Close the permission dialog first
    setShowPermissionDialog(false);
    
    // Add a small delay to ensure system permissions have taken effect
    setTimeout(async () => {
      await checkPrivileges();
      console.log('Privilege recheck completed');
    }, 1000);
  };

  const handleDialogClose = () => {
    setShowPermissionDialog(false);
  };

  if (!isVisible || hasPrivileges === null) {
    return null;
  }

  if (hasPrivileges) {
    return (
      <>
        <div className="mb-4 p-4 bg-green-500/10 border border-green-500/20 rounded-lg">
          <div className="flex items-center space-x-3">
            <Shield className="w-5 h-5 text-green-400" />
            <div className="flex-1">
              <h3 className="font-medium text-green-400">Privileges Verified</h3>
              <p className="text-sm text-green-300 mt-1">
                Application has the necessary privileges to create TUN devices and modify routing.
              </p>
            </div>
            <button
              onClick={handleClose}
              className="text-green-400 hover:text-green-300"
            >
              <X className="w-4 h-4" />
            </button>
          </div>
        </div>

        {/* macOS Permission Dialog */}
        <MacOSPermissionDialog
          isOpen={showPermissionDialog}
          onClose={handleDialogClose}
          onPermissionGranted={handlePermissionGranted}
        />

        {/* Success Notification */}
        <SuccessNotification
          show={showSuccessNotification}
          title="Permissions Granted!"
          message="TUN Proxy now has the necessary privileges to function properly."
          onClose={() => setShowSuccessNotification(false)}
        />
      </>
    );
  }

  return (
    <>
      <div className="mb-4 p-4 bg-yellow-500/10 border border-yellow-500/20 rounded-lg">
        <div className="flex items-start space-x-3">
          <AlertTriangle className="w-5 h-5 text-yellow-400 mt-0.5 flex-shrink-0" />
          <div className="flex-1">
            <h3 className="font-medium text-yellow-400">Elevated Privileges Required</h3>
            <p className="text-sm text-yellow-300 mt-1">
              TUN Proxy requires administrator/root privileges to function properly.
            </p>
            
            <div className="mt-3 p-3 bg-gray-800/50 rounded border border-gray-600">
              <h4 className="text-sm font-medium text-gray-200 mb-2">Instructions:</h4>
              <pre className="text-xs text-gray-300 whitespace-pre-wrap font-mono">
                {instructions}
              </pre>
            </div>

            <div className="mt-3 flex space-x-2">
              {isMacOS && (
                <button
                  onClick={requestPermissions}
                  className="btn-primary text-xs flex items-center space-x-1"
                >
                  <Zap className="w-3 h-3" />
                  <span>Grant Permissions</span>
                </button>
              )}
              <button
                onClick={checkPrivileges}
                disabled={isChecking}
                className="btn-secondary text-xs flex items-center space-x-1"
              >
                {isChecking ? (
                  <>
                    <div className="w-3 h-3 border border-gray-400 border-t-transparent rounded-full animate-spin"></div>
                    <span>Checking...</span>
                  </>
                ) : (
                  <>
                    <Settings className="w-3 h-3" />
                    <span>Recheck</span>
                  </>
                )}
              </button>
              <button
                onClick={handleClose}
                className="btn-secondary text-xs"
              >
                Dismiss
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* macOS Permission Dialog */}
      <MacOSPermissionDialog
        isOpen={showPermissionDialog}
        onClose={handleDialogClose}
        onPermissionGranted={handlePermissionGranted}
      />

      {/* Success Notification */}
      <SuccessNotification
        show={showSuccessNotification}
        title="Permissions Granted!"
        message="TUN Proxy now has the necessary privileges to function properly."
        onClose={() => setShowSuccessNotification(false)}
      />
    </>
  );
};

export default PrivilegeWarning;