import React from 'react';
import { <PERSON>, Loader2, Al<PERSON><PERSON>riangle } from 'lucide-react';
import { ConnectionInfo } from '../types';

interface ConnectionControlProps {
  connectionInfo: ConnectionInfo | null;
  onConnect: () => void;
  onDisconnect: () => void;
}

const ConnectionControl: React.FC<ConnectionControlProps> = ({
  connectionInfo,
  onConnect,
  onDisconnect,
}) => {
  const getConnectionState = () => {
    if (!connectionInfo) return 'disconnected';
    
    if (connectionInfo.status === 'Disconnected') return 'disconnected';
    if (connectionInfo.status === 'Connecting') return 'connecting';
    if (typeof connectionInfo.status === 'object' && 'Connected' in connectionInfo.status) return 'connected';
    if (typeof connectionInfo.status === 'object' && 'Error' in connectionInfo.status) return 'error';
    
    return 'disconnected';
  };

  const getStatusText = () => {
    const state = getConnectionState();
    
    switch (state) {
      case 'disconnected':
        return 'Click to Connect';
      case 'connecting':
        return 'Connecting...';
      case 'connected':
        if (connectionInfo?.duration_seconds) {
          const duration = connectionInfo.duration_seconds;
          const hours = Math.floor(duration / 3600);
          const minutes = Math.floor((duration % 3600) / 60);
          const seconds = duration % 60;
          return `Connected ${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
        }
        return 'Connected';
      case 'error':
        if (typeof connectionInfo?.status === 'object' && 'Error' in connectionInfo.status) {
          const errorMsg = connectionInfo.status.Error.message;
          // Truncate long privilege error messages for the button
          if (errorMsg.includes('Insufficient privileges')) {
            return 'Privilege Error - See Details';
          }
          return errorMsg.length > 50 ? errorMsg.substring(0, 47) + '...' : errorMsg;
        }
        return 'Connection Failed';
      default:
        return 'Unknown State';
    }
  };

  const getButtonClass = () => {
    const state = getConnectionState();
    const baseClass = 'w-32 h-32 rounded-full flex items-center justify-center transition-all duration-300 focus:outline-none focus:ring-4 focus:ring-offset-4 focus:ring-offset-gray-900';
    
    switch (state) {
      case 'disconnected':
        return `${baseClass} bg-gray-600 hover:bg-gray-500 text-gray-300 focus:ring-gray-500`;
      case 'connecting':
        return `${baseClass} bg-primary-600 text-white focus:ring-primary-500`;
      case 'connected':
        return `${baseClass} bg-green-600 hover:bg-green-700 text-white focus:ring-green-500`;
      case 'error':
        return `${baseClass} bg-red-600 hover:bg-red-700 text-white focus:ring-red-500`;
      default:
        return `${baseClass} bg-gray-600 text-gray-300`;
    }
  };

  const getIcon = () => {
    const state = getConnectionState();
    
    switch (state) {
      case 'connecting':
        return <Loader2 className="w-12 h-12 animate-spin" />;
      case 'error':
        return <AlertTriangle className="w-12 h-12" />;
      default:
        return <Power className="w-12 h-12" />;
    }
  };

  const handleClick = () => {
    const state = getConnectionState();
    
    if (state === 'disconnected' || state === 'error') {
      onConnect();
    } else if (state === 'connected') {
      onDisconnect();
    }
    // Don't allow clicks during connecting state
  };

  const isClickable = () => {
    const state = getConnectionState();
    return state !== 'connecting';
  };

  return (
    <div className="flex flex-col items-center space-y-4">
      <button
        onClick={handleClick}
        disabled={!isClickable()}
        className={getButtonClass()}
        aria-label={getStatusText()}
      >
        {getIcon()}
      </button>
      
      <div className="text-center">
        <p className="text-lg font-medium text-gray-200">
          {getStatusText()}
        </p>
      </div>
    </div>
  );
};

export default ConnectionControl;