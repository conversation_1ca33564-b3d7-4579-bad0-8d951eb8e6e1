# TUN Proxy - Cross-Platform Network Proxy Tool

A high-performance, cross-platform proxy application built with <PERSON>ust and Tauri. The application intercepts system-wide network traffic using a TUN virtual network interface and intelligently routes it based on user-defined modes and rules.

## Features

- **Cross-Platform Support**: Windows, macOS, and Linux
- **TUN Interface**: System-wide traffic interception using virtual network interface
- **Multiple Proxy Modes**:
  - **Global**: Route all traffic through selected proxy node
  - **Rule-Based**: Intelligent routing based on domain/IP rules
  - **Direct**: Bypass proxy for direct connections
- **Real-time Monitoring**: Live network speed charts and connection statistics
- **Modern UI**: Dark-themed interface built with React and Tailwind CSS
- **Encrypted Configuration**: AES-128-GCM encrypted remote configuration
- **Comprehensive Logging**: Real-time log viewer with filtering
- **Privilege Management**: Built-in privilege checking and user guidance
- **macOS Integration**: Native permission dialogs and automatic privilege requests

## Quick Start

### Prerequisites

- Rust 1.70+ with Cargo
- Node.js 18+ with npm
- Platform-specific requirements:
  - **Windows**: Visual Studio Build Tools
  - **macOS**: Xcode Command Line Tools
  - **Linux**: build-essential, libgtk-3-dev, libwebkit2gtk-4.0-dev

### Development

1. Install dependencies:
```bash
npm install
```

2. Start development server with elevated privileges:
```bash
# macOS/Linux
sudo npm run tauri dev

# Windows (run Command Prompt as Administrator)
npm run tauri dev
```

**Warning: Elevated Privileges Required**: TUN Proxy needs administrator/root access to create TUN devices and modify routing tables. 

- **macOS Users**: The app will automatically guide you through permission setup with native dialogs
- **Other Platforms**: See [PRIVILEGES.md](PRIVILEGES.md) for detailed setup instructions

### Building

```bash
npm run tauri build
```

## macOS Permission Management

TUN Proxy features **intelligent permission management** on macOS:

### 🚀 **Automatic Permission Requests**
- **Native Dialogs**: Triggers macOS system permission dialogs
- **Step-by-Step Guidance**: Clear instructions for each permission step
- **Real-time Status**: Live updates during permission request process

### 🎯 **Smart Detection**
- **Privilege Checking**: Automatically detects current permission level
- **Platform Awareness**: Enables macOS-specific features automatically
- **Status Indicators**: Visual feedback for permission status

### 💡 **User-Friendly Experience**
- **One-Click Setup**: "Grant Permissions" button for easy setup
- **Error Recovery**: Helpful error messages and retry options
- **Manual Fallback**: Alternative setup instructions if needed

See [MACOS_PERMISSIONS.md](MACOS_PERMISSIONS.md) for detailed macOS permission management documentation.

## Privilege Requirements

TUN Proxy requires elevated privileges to:
- Create TUN virtual network devices
- Modify system routing tables
- Intercept and route network traffic

The application includes built-in privilege checking and will show helpful instructions if insufficient privileges are detected.

### Quick Solutions:
- **macOS**: Click "Grant Permissions" in the app for guided setup
- **Linux**: Run with `sudo npm run tauri dev`
- **Windows**: Run Command Prompt as Administrator
- **Production**: See [PRIVILEGES.md](PRIVILEGES.md) for permanent solutions

## Architecture

### Backend (Rust)
- **TUN Management**: Cross-platform TUN device creation and management
- **Packet Processing**: Raw IP packet parsing using smoltcp
- **Rule Engine**: Flexible rule-based traffic routing
- **Proxy Interface**: Extensible CoreProxy trait for different proxy protocols
- **Privilege Management**: Cross-platform privilege checking and elevation
- **macOS Integration**: Native permission dialogs and system integration

### Frontend (Tauri + React)
- **Connection Control**: Large, intuitive connection button with status indicators
- **Mode Selection**: Easy switching between proxy modes
- **Real-time Charts**: Live network speed visualization
- **Log Viewer**: Sliding log panel with comprehensive logging
- **Privilege Warnings**: Built-in privilege status and guidance
- **macOS Dialogs**: Native permission request dialogs

## Usage

1. **Launch** the application with elevated privileges
2. **macOS**: Click "Grant Permissions" if prompted for automatic setup
3. **Check Privileges**: The app will show warnings if privileges are insufficient
4. **Select Mode**: Choose Global, Rule-based, or Direct mode
5. **Choose Node**: Select proxy server (if applicable)
6. **Connect**: Click the power button to start traffic interception

## Development Status

✅ **Core Architecture**: Complete proxy framework with TUN management
✅ **Cross-Platform**: Windows, macOS, Linux support with privilege handling
✅ **macOS Integration**: Native permission dialogs and automatic setup
✅ **Modern UI**: React frontend with real-time monitoring
✅ **Rule Engine**: Flexible traffic routing with multiple modes
✅ **Security**: Encrypted configuration and privilege management

🔧 **Ready for Extension**: Easy to add new proxy protocols and features

## Contributing

1. Fork the repository
2. Create a feature branch
3. Ensure you have the required privileges for testing
4. Make your changes and add tests
5. Submit a pull request

## License

MIT License - see LICENSE file for details.

## Acknowledgments

- [Tauri](https://tauri.app/) - Cross-platform app framework
- [smoltcp](https://github.com/smoltcp-rs/smoltcp) - Network packet processing
- [tun](https://github.com/meh/rust-tun) - TUN interface management
- [aws-lc-rs](https://github.com/aws/aws-lc-rs) - Cryptographic operations