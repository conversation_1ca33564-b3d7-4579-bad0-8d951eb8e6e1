# macOS 权限管理 - 主动权限请求

## 新功能概述

TUN Proxy 现在在 macOS 上支持**主动权限请求**，不再只是显示提示信息，而是引导用户完成权限配置。

## 功能特性

### 🚀 **主动权限请求**
- **系统对话框触发**: 自动弹出 macOS 系统权限对话框
- **用户友好界面**: 清晰的权限说明和操作指导
- **智能检测**: 自动检测当前权限状态
- **实时反馈**: 权限请求过程的实时状态更新

### 🎯 **智能权限检测**
- **多层检测**: 检查 root 权限、sudo 访问、TUN 设备访问
- **平台感知**: 自动识别 macOS 并启用相应功能
- **状态缓存**: 避免重复检查，提升用户体验

### 💡 **用户体验优化**
- **可视化指导**: 步骤化的权限请求流程
- **错误处理**: 详细的错误信息和解决方案
- **自动重试**: 支持权限请求失败后的重试机制

## 使用流程

### 1. 自动检测
应用启动时自动检测权限状态：
- ✅ **绿色横幅**: 权限充足，可以正常使用
- ⚠️ **黄色横幅**: 权限不足，显示"Grant Permissions"按钮

### 2. 主动请求权限
点击"Grant Permissions"按钮后：

#### 步骤 1: 权限说明
- 显示详细的权限需求说明
- 解释为什么需要这些权限
- 用户可以选择"Grant Permissions"或"Cancel"

#### 步骤 2: 系统权限对话框
- 自动触发 macOS 系统权限对话框
- 用户输入管理员密码
- 显示请求进度和指导信息

#### 步骤 3: 结果反馈
- **成功**: 显示绿色成功界面，自动关闭对话框
- **失败**: 显示错误信息和替代解决方案
- **取消**: 提供手动设置指导

### 3. 权限验证
权限请求完成后：
- 自动重新检测权限状态
- 更新 UI 显示当前状态
- 允许用户继续使用应用

## 技术实现

### 后端 (Rust)
```rust
// macOS 权限管理模块
pub struct MacOSPermissions;

impl MacOSPermissions {
    // 主动请求管理员权限
    pub async fn request_admin_privileges() -> Result<()>
    
    // 打开系统偏好设置
    pub async fn request_network_access() -> Result<()>
    
    // 显示原生 macOS 对话框
    pub async fn show_permission_dialog() -> Result<bool>
    
    // 交互式权限请求流程
    pub async fn request_permissions_interactive() -> Result<bool>
}
```

### 前端 (React)
```typescript
// macOS 权限对话框组件
const MacOSPermissionDialog: React.FC = () => {
  // 多步骤权限请求流程
  // 实时状态更新
  // 错误处理和重试
}

// 权限警告组件增强
const PrivilegeWarning: React.FC = () => {
  // 平台检测
  // 主动权限请求按钮
  // 状态管理
}
```

### 系统集成
- **osascript**: 触发系统权限对话框
- **系统偏好设置**: 自动打开相关设置页面
- **权限检测**: 多层权限状态验证

## 权限类型

### 1. 管理员权限
- **用途**: 创建 TUN 设备，修改路由表
- **获取方式**: 系统密码对话框
- **验证**: `sudo -n true` 检查

### 2. 网络配置权限
- **用途**: 访问网络接口配置
- **获取方式**: 系统偏好设置 → 网络
- **验证**: TUN 设备创建测试

### 3. 安全与隐私权限
- **用途**: 完整磁盘访问（如需要）
- **获取方式**: 系统偏好设置 → 安全性与隐私
- **验证**: 文件系统访问测试

## 错误处理

### 常见错误和解决方案

#### 1. 用户取消权限请求
```
错误: "Permission request was cancelled or denied"
解决: 提供手动设置指导，显示 sudo 命令
```

#### 2. 系统对话框未出现
```
错误: "Failed to show system dialog"
解决: 检查其他窗口遮挡，提供手动触发选项
```

#### 3. 权限请求超时
```
错误: "Permission request timeout"
解决: 提示用户检查系统对话框，提供重试选项
```

## 用户指导

### 首次使用
1. 启动应用后查看权限状态横幅
2. 如显示黄色警告，点击"Grant Permissions"
3. 在弹出的对话框中点击"Grant Permissions"
4. 在系统对话框中输入管理员密码
5. 等待权限配置完成

### 权限被拒绝后
1. 查看错误信息和建议解决方案
2. 可以选择"Try Again"重新请求
3. 或者使用手动方式：`sudo npm run tauri dev`

### 系统设置检查
1. 系统偏好设置 → 安全性与隐私 → 隐私
2. 检查"完整磁盘访问"中是否包含应用
3. 系统偏好设置 → 网络 → 高级 → 代理

## 开发者说明

### 启用 macOS 权限功能
```rust
#[cfg(target_os = "macos")]
use tun_proxy::MacOSPermissions;

// 在应用中使用
let granted = MacOSPermissions::request_permissions_interactive().await?;
```

### 前端集成
```typescript
// 检测平台
const isMacOS = navigator.userAgent.toLowerCase().includes('mac');

// 请求权限
const granted = await invoke<boolean>('request_permissions');
```

### 自定义权限流程
可以根据需要自定义权限请求流程：
- 修改对话框文本和样式
- 添加额外的权限检查
- 集成其他系统权限

## 安全考虑

### 权限最小化
- 只请求必要的权限
- 权限请求后立即验证
- 提供权限撤销说明

### 用户透明度
- 清晰说明权限用途
- 显示权限请求过程
- 提供详细的错误信息

### 系统集成
- 使用官方 macOS API
- 遵循系统权限模型
- 支持系统权限管理

## 未来改进

### 计划功能
1. **权限缓存**: 避免重复请求
2. **自动权限恢复**: 应用更新后自动恢复权限
3. **细粒度权限**: 分步骤请求不同类型权限
4. **权限监控**: 实时监控权限状态变化

### 性能优化
1. **异步权限检查**: 避免阻塞 UI
2. **智能重试**: 根据错误类型智能重试
3. **状态持久化**: 缓存权限状态

这个新的权限管理系统大大改善了 macOS 用户的使用体验，从被动提示变为主动引导，让用户能够轻松完成权限配置。