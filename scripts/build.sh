#!/bin/bash

# Build script for TUN Proxy

echo "🏗️  Building TUN Proxy for production..."

# Check if Node.js is installed
if ! command -v node &> /dev/null; then
    echo "❌ Node.js is not installed. Please install Node.js 18+ first."
    exit 1
fi

# Check if Rust is installed
if ! command -v cargo &> /dev/null; then
    echo "❌ Rust is not installed. Please install Rust first."
    exit 1
fi

# Install dependencies if needed
if [ ! -d "node_modules" ]; then
    echo "📦 Installing npm dependencies..."
    npm install
fi

# Build the application
echo "🔨 Building application..."
npm run tauri build

echo "✅ Build complete! Check src-tauri/target/release/bundle/ for output files."