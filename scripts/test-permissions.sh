#!/bin/bash

echo "🧪 Testing TUN Proxy Permission System"
echo "======================================"

# Test 1: Check current user
echo "📋 Test 1: Current User Information"
echo "Current UID: $(id -u)"
echo "Current User: $(whoami)"
echo "Groups: $(groups)"
echo ""

# Test 2: Check sudo access
echo "📋 Test 2: Sudo Access Test"
if sudo -n true 2>/dev/null; then
    echo "✅ Sudo access: Available (passwordless)"
else
    echo "❌ Sudo access: Not available (requires password)"
fi
echo ""

# Test 3: Check route command access
echo "📋 Test 3: Route Command Access"
if sudo -n route -n get default >/dev/null 2>&1; then
    echo "✅ Route command: Accessible"
else
    echo "❌ Route command: Not accessible"
fi
echo ""

# Test 4: Check TUN device access
echo "📋 Test 4: TUN Device Access"
if ls /dev/utun* >/dev/null 2>&1; then
    echo "✅ TUN devices: Available"
    echo "   Found devices: $(ls /dev/utun* 2>/dev/null | tr '\n' ' ')"
else
    echo "❌ TUN devices: Not found"
fi
echo ""

# Test 5: Check if running as root
echo "📋 Test 5: Root Privileges"
if [ "$EUID" -eq 0 ]; then
    echo "✅ Root privileges: Running as root"
else
    echo "❌ Root privileges: Not running as root"
fi
echo ""

# Test 6: Test privilege helper script creation
echo "📋 Test 6: Script Creation Test"
TEMP_SCRIPT="/tmp/tun_proxy_test_$$"
cat > "$TEMP_SCRIPT" << 'EOF'
#!/bin/bash
echo "Test script executed successfully"
EOF

if chmod +x "$TEMP_SCRIPT" 2>/dev/null; then
    echo "✅ Script creation: Success"
    rm -f "$TEMP_SCRIPT"
else
    echo "❌ Script creation: Failed"
fi
echo ""

# Summary
echo "📊 Summary"
echo "=========="

TOTAL_TESTS=6
PASSED_TESTS=0

# Count passed tests
if [ "$(id -u)" = "0" ] || sudo -n true 2>/dev/null; then
    ((PASSED_TESTS++))
fi

if sudo -n route -n get default >/dev/null 2>&1; then
    ((PASSED_TESTS++))
fi

if ls /dev/utun* >/dev/null 2>&1; then
    ((PASSED_TESTS++))
fi

echo "Tests passed: $PASSED_TESTS/$TOTAL_TESTS"

if [ "$PASSED_TESTS" -eq "$TOTAL_TESTS" ]; then
    echo "🎉 All tests passed! TUN Proxy should work correctly."
elif [ "$PASSED_TESTS" -ge 2 ]; then
    echo "⚠️  Some tests failed. TUN Proxy may work with elevated privileges."
else
    echo "❌ Most tests failed. TUN Proxy will likely need permission setup."
fi

echo ""
echo "💡 To run TUN Proxy with proper privileges:"
echo "   sudo npm run tauri dev"
echo ""
echo "🔧 Or use the app's built-in permission request on macOS"