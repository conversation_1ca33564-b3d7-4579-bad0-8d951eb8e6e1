{"build": {"beforeDevCommand": "npm run dev", "beforeBuildCommand": "npm run build", "devPath": "http://localhost:1420", "distDir": "../dist", "withGlobalTauri": false}, "package": {"productName": "TUN Proxy", "version": "0.1.0"}, "tauri": {"allowlist": {"all": false, "shell": {"all": false, "open": true}, "window": {"all": false, "close": true, "hide": true, "show": true, "maximize": true, "minimize": true, "unmaximize": true, "unminimize": true, "startDragging": true}, "fs": {"all": false, "readFile": false, "writeFile": false, "readDir": false, "copyFile": false, "createDir": false, "removeDir": false, "removeFile": false, "renameFile": false, "exists": false}}, "bundle": {"active": true, "targets": "all", "identifier": "com.tunproxy.app", "icon": ["icons/32x32.png", "icons/128x128.png", "icons/<EMAIL>", "icons/icon.icns", "icons/icon.ico"]}, "security": {"csp": null}, "windows": [{"fullscreen": false, "resizable": false, "title": "TUN Proxy", "width": 800, "height": 600, "minWidth": 800, "minHeight": 600, "maxWidth": 800, "maxHeight": 600, "center": true, "decorations": true, "alwaysOnTop": false, "skipTaskbar": false}]}}