use std::sync::Arc;
use tauri::State;
use tokio::sync::Mutex;
use serde::{Deserialize, Serialize};

use crate::app_state::{AppState, ConnectionStatus, LogEntry};
use tun_proxy::{ProxyMode, NetworkStats, PrivilegeManager};

#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct ConnectionInfo {
    pub status: ConnectionStatus,
    pub duration_seconds: Option<i64>,
}

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct NetworkStatsResponse {
    pub bytes_sent: u64,
    pub bytes_received: u64,
    pub packets_sent: u64,
    pub packets_received: u64,
    pub connections_active: u32,
    pub connections_total: u64,
    pub upload_speed: f64,
    pub download_speed: f64,
}

impl From<NetworkStats> for NetworkStatsResponse {
    fn from(stats: NetworkStats) -> Self {
        Self {
            bytes_sent: stats.bytes_sent,
            bytes_received: stats.bytes_received,
            packets_sent: stats.packets_sent,
            packets_received: stats.packets_received,
            connections_active: stats.connections_active,
            connections_total: stats.connections_total,
            upload_speed: stats.get_upload_speed(60.0), // Speed over last 60 seconds
            download_speed: stats.get_download_speed(60.0),
        }
    }
}

/// Connect to the proxy
#[tauri::command]
pub async fn connect_proxy(
    state: State<'_, Arc<Mutex<AppState>>>,
) -> Result<ConnectionInfo, String> {
    let mut app_state = state.lock().await;
    
    match app_state.connect().await {
        Ok(()) => {
            let duration = app_state.get_connection_duration()
                .map(|d| d.num_seconds());
            
            Ok(ConnectionInfo {
                status: app_state.connection_status.clone(),
                duration_seconds: duration,
            })
        }
        Err(e) => Err(e),
    }
}

/// Disconnect from the proxy
#[tauri::command]
pub async fn disconnect_proxy(
    state: State<'_, Arc<Mutex<AppState>>>,
) -> Result<ConnectionInfo, String> {
    let mut app_state = state.lock().await;
    
    match app_state.disconnect().await {
        Ok(()) => {
            Ok(ConnectionInfo {
                status: app_state.connection_status.clone(),
                duration_seconds: None,
            })
        }
        Err(e) => Err(e),
    }
}

/// Get current connection status
#[tauri::command]
pub async fn get_connection_status(
    state: State<'_, Arc<Mutex<AppState>>>,
) -> Result<ConnectionInfo, String> {
    let app_state = state.lock().await;
    let duration = app_state.get_connection_duration()
        .map(|d| d.num_seconds());
    
    Ok(ConnectionInfo {
        status: app_state.connection_status.clone(),
        duration_seconds: duration,
    })
}

/// Set proxy mode
#[tauri::command]
pub async fn set_proxy_mode(
    mode: String,
    state: State<'_, Arc<Mutex<AppState>>>,
) -> Result<(), String> {
    let proxy_mode = match mode.as_str() {
        "Global" => ProxyMode::Global,
        "Rule" => ProxyMode::Rule,
        "Direct" => ProxyMode::Direct,
        _ => return Err(format!("Invalid proxy mode: {}", mode)),
    };

    let mut app_state = state.lock().await;
    app_state.set_proxy_mode(proxy_mode).await
}

/// Get current proxy mode
#[tauri::command]
pub async fn get_proxy_mode(
    state: State<'_, Arc<Mutex<AppState>>>,
) -> Result<String, String> {
    let app_state = state.lock().await;
    let mode_str = match app_state.proxy_mode {
        ProxyMode::Global => "Global",
        ProxyMode::Rule => "Rule",
        ProxyMode::Direct => "Direct",
    };
    Ok(mode_str.to_string())
}

/// Set selected node
#[tauri::command]
pub async fn set_selected_node(
    node_name: Option<String>,
    state: State<'_, Arc<Mutex<AppState>>>,
) -> Result<(), String> {
    let mut app_state = state.lock().await;
    app_state.set_selected_node(node_name).await
}

/// Get selected node
#[tauri::command]
pub async fn get_selected_node(
    state: State<'_, Arc<Mutex<AppState>>>,
) -> Result<Option<String>, String> {
    let app_state = state.lock().await;
    Ok(app_state.selected_node.clone())
}

/// Get available nodes
#[tauri::command]
pub async fn get_available_nodes(
    state: State<'_, Arc<Mutex<AppState>>>,
) -> Result<Vec<String>, String> {
    let app_state = state.lock().await;
    Ok(app_state.available_nodes.clone())
}

/// Get network statistics
#[tauri::command]
pub async fn get_network_stats(
    state: State<'_, Arc<Mutex<AppState>>>,
) -> Result<NetworkStatsResponse, String> {
    let mut app_state = state.lock().await;
    app_state.update_network_stats().await;
    Ok(NetworkStatsResponse::from(app_state.network_stats.clone()))
}

/// Get application logs
#[tauri::command]
pub async fn get_logs(
    state: State<'_, Arc<Mutex<AppState>>>,
) -> Result<Vec<LogEntry>, String> {
    let app_state = state.lock().await;
    Ok(app_state.logs.clone())
}

/// Clear application logs
#[tauri::command]
pub async fn clear_logs(
    state: State<'_, Arc<Mutex<AppState>>>,
) -> Result<(), String> {
    let mut app_state = state.lock().await;
    app_state.clear_logs();
    Ok(())
}

/// Check if the application has necessary privileges
#[tauri::command]
pub async fn check_privileges() -> Result<bool, String> {
    // Force a fresh check each time
    match PrivilegeManager::check_privileges() {
        Ok(()) => {
            println!("Privilege check: PASSED");
            Ok(true)
        },
        Err(e) => {
            println!("Privilege check: FAILED - {}", e);
            Ok(false)
        },
    }
}

/// Get privilege instructions for the current platform
#[tauri::command]
pub async fn get_privilege_instructions() -> Result<String, String> {
    Ok(PrivilegeManager::get_privilege_instructions())
}

/// Request permissions interactively (macOS only)
#[tauri::command]
pub async fn request_permissions() -> Result<bool, String> {
    #[cfg(target_os = "macos")]
    {
        match PrivilegeManager::request_macos_permissions().await {
            Ok(granted) => Ok(granted),
            Err(e) => Err(format!("Failed to request permissions: {}", e)),
        }
    }
    
    #[cfg(not(target_os = "macos"))]
    {
        Err("Interactive permission request is only available on macOS".to_string())
    }
}