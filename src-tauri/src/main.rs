#![cfg_attr(not(debug_assertions), windows_subsystem = "windows")]

mod app_state;
mod commands;
mod logging;

use std::sync::Arc;
use tokio::sync::Mutex;
use tracing::info;

use app_state::AppState;
use commands::*;
use logging::setup_logging;

#[tokio::main]
async fn main() {
    // Initialize logging
    setup_logging();

    info!("Starting TUN Proxy application");

    // Create application state
    let app_state = Arc::new(Mutex::new(AppState::new().await));

    tauri::Builder::default()
        .manage(app_state)
        .invoke_handler(tauri::generate_handler![
            connect_proxy,
            disconnect_proxy,
            get_connection_status,
            set_proxy_mode,
            get_proxy_mode,
            set_selected_node,
            get_selected_node,
            get_available_nodes,
            get_network_stats,
            get_logs,
            clear_logs,
            check_privileges,
            get_privilege_instructions,
            request_permissions
        ])
        .setup(|_app| {
            info!("Tauri application setup complete");
            Ok(())
        })
        .run(tauri::generate_context!())
        .expect("error while running tauri application");
}