use std::sync::Arc;
use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};

// Re-export the core library modules
use tun_proxy::{
    TunManager, ConfigManager, DummyProxy, ProxyMode, NetworkStats, CoreProxy, PrivilegeManager
};

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub enum ConnectionStatus {
    Disconnected,
    Connecting,
    Connected { since: DateTime<Utc> },
    Error { message: String },
}

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct LogEntry {
    pub timestamp: DateTime<Utc>,
    pub level: String,
    pub message: String,
}

/// Main application state
pub struct AppState {
    pub tun_manager: Option<TunManager>,
    pub config_manager: ConfigManager,
    pub connection_status: ConnectionStatus,
    pub proxy_mode: ProxyMode,
    pub selected_node: Option<String>,
    pub available_nodes: Vec<String>,
    pub logs: Vec<LogEntry>,
    pub network_stats: NetworkStats,
}

impl AppState {
    pub async fn new() -> Self {
        let mut config_manager = ConfigManager::new();
        
        // For development, use dummy config
        let config = config_manager.create_dummy_config();
        
        // Create dummy proxy and configure it
        let mut proxy = DummyProxy::new();
        if let Err(e) = proxy.configure(&config.node_config) {
            eprintln!("Failed to configure proxy: {}", e);
        }
        
        let available_nodes = proxy.get_node_names();
        let selected_node = available_nodes.first().cloned();

        Self {
            tun_manager: None,
            config_manager,
            connection_status: ConnectionStatus::Disconnected,
            proxy_mode: ProxyMode::Rule,
            selected_node,
            available_nodes,
            logs: Vec::new(),
            network_stats: NetworkStats::new(),
        }
    }

    pub async fn connect(&mut self) -> Result<(), String> {
        if matches!(self.connection_status, ConnectionStatus::Connected { .. }) {
            return Err("Already connected".to_string());
        }

        self.connection_status = ConnectionStatus::Connecting;

        // Check privileges before attempting to connect
        if let Err(e) = PrivilegeManager::check_privileges() {
            let error_msg = format!(
                "Insufficient privileges to start TUN proxy.\n\n{}\n\nError: {}", 
                PrivilegeManager::get_privilege_instructions(),
                e
            );
            self.connection_status = ConnectionStatus::Error { message: error_msg.clone() };
            self.add_log("ERROR", &error_msg);
            return Err(error_msg);
        }

        // Create proxy instance
        let mut proxy = DummyProxy::new();
        let config = self.config_manager.get_config()
            .ok_or_else(|| "No configuration available".to_string())?;
        
        proxy.configure(&config.node_config)
            .map_err(|e| format!("Failed to configure proxy: {}", e))?;

        // Create TUN manager
        let mut tun_manager = TunManager::new(Arc::new(proxy));
        
        // Configure rule engine
        {
            let rule_engine = tun_manager.get_rule_engine();
            let mut engine = rule_engine.lock().await;
            engine.set_config(config.clone());
            engine.set_mode(self.proxy_mode.clone());
            engine.set_selected_node(self.selected_node.clone());
        }

        // Start TUN manager
        match tun_manager.start().await {
            Ok(()) => {
                self.tun_manager = Some(tun_manager);
                self.connection_status = ConnectionStatus::Connected { since: Utc::now() };
                self.add_log("INFO", "Successfully connected to proxy");
                Ok(())
            }
            Err(e) => {
                let error_msg = format!("Failed to start TUN manager: {}", e);
                self.connection_status = ConnectionStatus::Error { message: error_msg.clone() };
                self.add_log("ERROR", &error_msg);
                Err(error_msg)
            }
        }
    }

    pub async fn disconnect(&mut self) -> Result<(), String> {
        if let Some(mut tun_manager) = self.tun_manager.take() {
            match tun_manager.stop().await {
                Ok(()) => {
                    self.connection_status = ConnectionStatus::Disconnected;
                    self.add_log("INFO", "Disconnected from proxy");
                    Ok(())
                }
                Err(e) => {
                    let error_msg = format!("Failed to stop TUN manager: {}", e);
                    self.connection_status = ConnectionStatus::Error { message: error_msg.clone() };
                    self.add_log("ERROR", &error_msg);
                    Err(error_msg)
                }
            }
        } else {
            self.connection_status = ConnectionStatus::Disconnected;
            Ok(())
        }
    }

    pub async fn set_proxy_mode(&mut self, mode: ProxyMode) -> Result<(), String> {
        self.proxy_mode = mode.clone();
        
        if let Some(ref tun_manager) = self.tun_manager {
            let rule_engine = tun_manager.get_rule_engine();
            let mut engine = rule_engine.lock().await;
            engine.set_mode(mode);
        }

        self.add_log("INFO", &format!("Proxy mode changed to {:?}", self.proxy_mode));
        Ok(())
    }

    pub async fn set_selected_node(&mut self, node_name: Option<String>) -> Result<(), String> {
        if let Some(ref node) = node_name {
            if !self.available_nodes.contains(node) {
                return Err(format!("Node '{}' not available", node));
            }
        }

        self.selected_node = node_name.clone();
        
        if let Some(ref tun_manager) = self.tun_manager {
            let rule_engine = tun_manager.get_rule_engine();
            let mut engine = rule_engine.lock().await;
            engine.set_selected_node(node_name.clone());
        }

        let log_msg = match node_name {
            Some(node) => format!("Selected node changed to {}", node),
            None => "No node selected".to_string(),
        };
        self.add_log("INFO", &log_msg);
        Ok(())
    }

    pub async fn update_network_stats(&mut self) {
        if let Some(ref tun_manager) = self.tun_manager {
            self.network_stats = tun_manager.get_stats().await;
        }
    }

    pub fn add_log(&mut self, level: &str, message: &str) {
        let entry = LogEntry {
            timestamp: Utc::now(),
            level: level.to_string(),
            message: message.to_string(),
        };
        
        self.logs.push(entry);
        
        // Keep only the last 1000 log entries
        if self.logs.len() > 1000 {
            self.logs.drain(0..self.logs.len() - 1000);
        }
    }

    pub fn clear_logs(&mut self) {
        self.logs.clear();
        self.add_log("INFO", "Logs cleared");
    }

    pub fn get_connection_duration(&self) -> Option<chrono::Duration> {
        if let ConnectionStatus::Connected { since } = &self.connection_status {
            Some(Utc::now() - *since)
        } else {
            None
        }
    }
}