[package]
name = "tun-proxy-tauri"
version = "0.1.0"
edition = "2021"

[build-dependencies]
tauri-build = { version = "1.0", features = [] }

[dependencies]
# Tauri framework
tauri = { version = "1.0", features = ["shell-open", "window-close", "window-hide", "window-maximize", "window-minimize", "window-show", "window-start-dragging", "window-unmaximize", "window-unminimize"] }
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"

# Core async runtime
tokio = { version = "1.0", features = ["full"] }

# TUN interface management
tun = "0.6"

# Packet parsing
smoltcp = "0.11"

# Cryptography
aws-lc-rs = "1.0"

# Async traits
async-trait = "0.1"

# Logging
tracing = "0.1"
tracing-subscriber = { version = "0.3", features = ["env-filter", "json"] }

# Network utilities
socket2 = "0.5"
ipnet = "2.9"

# HTTP client for config fetching
reqwest = { version = "0.11", features = ["json"] }

# Error handling
anyhow = "1.0"
thiserror = "1.0"

# Cross-platform system utilities
sysinfo = "0.30"

# Time utilities
chrono = { version = "0.4", features = ["serde"] }

# Hex encoding/decoding
hex = "0.4"

# Local tun-proxy library
tun-proxy = { path = ".." }


[features]
default = ["custom-protocol"]
custom-protocol = ["tauri/custom-protocol"]
