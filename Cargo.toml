[package]
name = "tun-proxy"
version = "0.1.0"
edition = "2021"
authors = ["Your Name <<EMAIL>>"]
description = "Cross-platform TUN-based proxy tool with <PERSON><PERSON> and <PERSON>ri"

[workspace]
members = ["src-tauri"]

[dependencies]
# Core async runtime
tokio = { version = "1.0", features = ["full"] }

# TUN interface management
tun = "0.6"

# Packet parsing
smoltcp = "0.11"

# Cryptography
aws-lc-rs = "1.0"

# Async traits
async-trait = "0.1"

# Logging
tracing = "0.1"
tracing-subscriber = { version = "0.3", features = ["env-filter", "json"] }

# Network utilities
socket2 = "0.5"
ipnet = "2.9"

# Configuration and serialization
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"
toml = "0.8"

# HTTP client for config fetching
reqwest = { version = "0.11", features = ["json"] }

# Error handling
anyhow = "1.0"
thiserror = "1.0"

# Cross-platform system utilities
sysinfo = "0.30"

# Hex encoding/decoding
hex = "0.4"


[dev-dependencies]
tokio-test = "0.4"

[[bin]]
name = "tun-proxy"
path = "src/main.rs"