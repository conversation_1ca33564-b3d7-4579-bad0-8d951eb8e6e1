# TUN Proxy - Privilege Requirements and Setup

## Why Privileges Are Required

TUN Proxy requires elevated privileges because it needs to:

1. **Create TUN devices** - Virtual network interfaces that intercept system traffic
2. **Modify routing tables** - Direct traffic through the TUN interface
3. **Access low-level networking APIs** - For packet capture and injection

## Platform-Specific Setup

### macOS

#### Option 1: Run with sudo (Recommended for Development)
```bash
# For development
sudo npm run tauri dev

# For built application
sudo ./target/release/bundle/macos/TUN\ Proxy.app/Contents/MacOS/tun-proxy-tauri
```

#### Option 2: Grant Persistent Permissions
1. **System Preferences** → **Security & Privacy** → **Privacy** → **Full Disk Access**
2. Add your terminal application (Terminal.app, iTerm2, etc.)
3. **System Preferences** → **Network** → **Advanced** → **Proxies**
4. Note: This may not be sufficient for TUN device creation

#### Option 3: Code Signing and Entitlements (Production)
For production apps, you'll need:
```xml
<!-- Entitlements.plist -->
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
    <key>com.apple.security.network.client</key>
    <true/>
    <key>com.apple.security.network.server</key>
    <true/>
</dict>
</plist>
```

### Linux

#### Option 1: Run with sudo
```bash
# For development
sudo npm run tauri dev

# For built application
sudo ./target/release/tun-proxy-tauri
```

#### Option 2: Grant CAP_NET_ADMIN Capability
```bash
# Build the application first
npm run tauri build

# Grant capability (more secure than sudo)
sudo setcap cap_net_admin+ep ./target/release/tun-proxy-tauri

# Now you can run without sudo
./target/release/tun-proxy-tauri
```

#### Option 3: Add User to netdev Group (Ubuntu/Debian)
```bash
# Add user to netdev group
sudo usermod -a -G netdev $USER

# Logout and login again, then:
./target/release/tun-proxy-tauri
```

### Windows

#### Option 1: Run as Administrator
1. Right-click the application
2. Select "Run as administrator"

#### Option 2: Elevated Command Prompt
```cmd
# Open Command Prompt as Administrator
# Navigate to project directory
npm run tauri dev
```

#### Option 3: PowerShell as Administrator
```powershell
# Open PowerShell as Administrator
# Navigate to project directory
npm run tauri dev
```

## Development Workflow

### Quick Start (All Platforms)
```bash
# Clone the repository
git clone <repository-url>
cd tun-proxy

# Install dependencies
npm install

# Run with elevated privileges
sudo npm run tauri dev  # macOS/Linux
# OR run Command Prompt as Administrator, then: npm run tauri dev  # Windows
```

### Building for Distribution

#### macOS
```bash
# Build the app
npm run tauri build

# The app will be in: src-tauri/target/release/bundle/macos/
# Users will need to run with sudo or grant permissions
```

#### Linux
```bash
# Build the app
npm run tauri build

# Grant capabilities for distribution
sudo setcap cap_net_admin+ep ./src-tauri/target/release/tun-proxy-tauri

# Package with capabilities intact
# Note: Some package managers may strip capabilities
```

#### Windows
```bash
# Build the app
npm run tauri build

# The executable will be in: src-tauri/target/release/
# Users must run as Administrator
```

## Troubleshooting

### "Operation not permitted" on macOS
- Ensure you're running with `sudo`
- Check System Integrity Protection (SIP) status: `csrutil status`
- Try disabling SIP temporarily for development (not recommended for production)

### "Permission denied" on Linux
- Verify you're running with `sudo` or have `CAP_NET_ADMIN`
- Check if `/dev/net/tun` exists: `ls -la /dev/net/tun`
- Ensure TUN/TAP support is enabled in kernel

### "Access denied" on Windows
- Ensure you're running as Administrator
- Check if TUN/TAP driver is installed (OpenVPN, WireGuard, etc.)
- Verify Windows Defender isn't blocking the application

### Application Shows Privilege Warning
The app includes built-in privilege checking:

1. **Yellow Warning**: Insufficient privileges detected
   - Follow the platform-specific instructions shown
   - Click "Recheck Privileges" after granting permissions

2. **Green Confirmation**: Privileges verified
   - You can proceed to connect

3. **Connection Fails**: Even with privileges
   - Check logs for specific error messages
   - Ensure TUN/TAP drivers are installed
   - Verify no other VPN software is conflicting

## Security Considerations

### Why Root/Administrator Access?
- **TUN Device Creation**: Requires kernel-level access
- **Routing Table Modification**: System-level network changes
- **Raw Socket Access**: Low-level packet manipulation

### Minimizing Risk
1. **Review Code**: The application is open source
2. **Capability-based**: Use `CAP_NET_ADMIN` on Linux instead of full root
3. **Temporary Elevation**: Only elevate when needed
4. **Sandboxing**: Consider running in containers for testing

### Production Deployment
- **Code Signing**: Sign applications for distribution
- **Installer Packages**: Use proper installers that handle permissions
- **User Education**: Clearly explain why privileges are needed
- **Privilege Dropping**: Drop privileges after setup (future enhancement)

## Alternative Approaches

### User-Space Alternatives
- **Proxy-only Mode**: HTTP/SOCKS proxy without TUN (lower privileges)
- **Browser Extensions**: For web traffic only
- **Application-specific**: Hook specific applications instead of system-wide

### Container-based Development
```bash
# Docker with privileged mode
docker run --privileged -it ubuntu:latest

# Inside container
apt update && apt install -y nodejs npm rust
# ... setup and run application
```

## Future Improvements

1. **Privilege Dropping**: Drop privileges after TUN setup
2. **Helper Daemon**: Separate privileged helper process
3. **User-space TUN**: Explore user-space TUN implementations
4. **Installer Integration**: Proper installers that handle permissions
5. **Capability Refinement**: Use minimal required capabilities

## Getting Help

If you encounter privilege-related issues:

1. **Check the in-app privilege warning** for platform-specific instructions
2. **Review the logs** in the application for detailed error messages
3. **Verify TUN/TAP support** is available on your system
4. **Test with a simple TUN example** to isolate issues
5. **Check for conflicting software** (other VPNs, security software)

Remember: These privilege requirements are fundamental to how TUN-based networking works and are not specific to this application.