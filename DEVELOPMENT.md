# TUN Proxy Development Guide

## Project Status

✅ **Core Architecture Implemented**
- <PERSON>ust backend with CoreProxy trait system
- Cross-platform TUN device management framework
- Rule-based traffic routing engine
- Encrypted configuration management
- Tauri-based desktop application
- Modern React frontend with dark theme

## What's Working

### Backend (Rust)
- ✅ CoreProxy trait definition with TCP/UDP handling
- ✅ DummyProxy implementation for testing
- ✅ Rule engine with Global/Rule/Direct modes
- ✅ Configuration management with AES-128-GCM encryption
- ✅ Network packet parsing framework (smoltcp)
- ✅ Cross-platform routing table management
- ✅ Error handling and logging infrastructure

### Frontend (React + Tauri)
- ✅ Connection control with status indicators
- ✅ Mode selection (Global/Rule/Direct)
- ✅ Node selection dropdown
- ✅ Real-time network speed charts
- ✅ Log viewer with filtering and controls
- ✅ Modern dark theme UI
- ✅ Responsive design

### Build System
- ✅ Cargo workspace configuration
- ✅ Tauri integration
- ✅ TypeScript compilation
- ✅ Vite bundling
- ✅ Cross-platform build scripts

## Implementation Notes

### TUN Device Management
The current implementation includes a placeholder for TUN device management. The actual TUN interface creation and packet handling would require:

1. **Platform-specific implementations**:
   - Windows: WinTUN driver integration
   - macOS: utun interface management
   - Linux: tun/tap device creation

2. **Packet I/O**: Real packet reading/writing from TUN device
3. **Traffic relay**: Bidirectional data flow between TUN and proxy

### Proxy Protocol Integration
The CoreProxy trait provides a clean interface for integrating various proxy protocols:

- Implement `handle_tcp_stream` for TCP connections
- Implement `handle_udp_association` for UDP traffic
- Return appropriate `ProxyTcpStream` and `ProxyUdpHandler` implementations

### Configuration Security
- Configuration is fetched from remote URL
- AES-128-GCM encryption with hardcoded key
- Memory-only storage (never written to disk)
- Supports node configuration and routing rules

## Development Workflow

### Prerequisites
```bash
# Install Rust
curl --proto '=https' --tlsv1.2 -sSf https://sh.rustup.rs | sh

# Install Node.js 18+
# Install platform-specific dependencies:
# - Windows: Visual Studio Build Tools
# - macOS: Xcode Command Line Tools  
# - Linux: build-essential, libgtk-3-dev, libwebkit2gtk-4.0-dev
```

### Development
```bash
# Install dependencies
npm install

# Start development server
npm run tauri dev
# or use the script
./scripts/dev.sh
```

### Building
```bash
# Build for production
npm run tauri build
# or use the script
./scripts/build.sh
```

### Testing
```bash
# Test Rust backend
cargo test

# Check compilation
cargo check
cd src-tauri && cargo check
```

## Next Steps for Production

### 1. TUN Device Implementation
- Integrate platform-specific TUN libraries
- Implement actual packet capture and injection
- Handle MTU and fragmentation

### 2. Proxy Protocol Support
- Implement specific proxy protocols (SOCKS5, HTTP, custom)
- Add authentication mechanisms
- Handle connection pooling and keep-alive

### 3. Routing Enhancement
- Implement DNS resolution for rule matching
- Add GeoIP-based routing
- Support for PAC (Proxy Auto-Configuration) files

### 4. Security Hardening
- Implement proper key management
- Add certificate pinning for configuration fetching
- Sandbox the application appropriately

### 5. Performance Optimization
- Implement zero-copy packet processing
- Add connection multiplexing
- Optimize memory usage for high-throughput scenarios

### 6. User Experience
- Add system tray integration
- Implement auto-start functionality
- Add import/export for configurations
- Create installer packages

### 7. Monitoring and Diagnostics
- Add detailed connection logging
- Implement performance metrics
- Create diagnostic tools for troubleshooting

## Architecture Decisions

### Why Tauri?
- Cross-platform desktop app with web technologies
- Smaller bundle size compared to Electron
- Native performance with Rust backend
- Secure by default with minimal API surface

### Why smoltcp?
- Pure Rust implementation
- No unsafe code
- Excellent for packet parsing
- Well-maintained and documented

### Why Rule-Based Routing?
- Flexible traffic management
- User-friendly configuration
- Supports complex routing scenarios
- Easy to extend with new rule types

## Contributing

1. Follow Rust coding standards
2. Use conventional commits
3. Add tests for new functionality
4. Update documentation
5. Ensure cross-platform compatibility

## License

MIT License - see LICENSE file for details.