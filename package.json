{"name": "tun-proxy-frontend", "private": true, "version": "0.1.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview", "tauri": "tauri"}, "dependencies": {"@tauri-apps/api": "^1.5.0", "react": "^18.2.0", "react-dom": "^18.2.0", "recharts": "^2.8.0", "lucide-react": "^0.294.0", "clsx": "^2.0.0"}, "devDependencies": {"@tauri-apps/cli": "^1.5.0", "@types/react": "^18.2.37", "@types/react-dom": "^18.2.15", "@vitejs/plugin-react": "^4.1.1", "autoprefixer": "^10.4.16", "postcss": "^8.4.31", "tailwindcss": "^3.3.5", "typescript": "^5.2.2", "vite": "^4.5.0"}}